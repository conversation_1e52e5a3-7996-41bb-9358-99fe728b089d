{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\types.move", "definition_location": {"file_hash": [7, 104, 78, 160, 210, 177, 46, 35, 129, 112, 10, 106, 124, 79, 141, 238, 66, 236, 155, 211, 143, 140, 114, 87, 115, 74, 91, 3, 196, 105, 0, 212], "start": 127, "end": 132}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "types"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [7, 104, 78, 160, 210, 177, 46, 35, 129, 112, 10, 106, 124, 79, 141, 238, 66, 236, 155, 211, 143, 140, 114, 87, 115, 74, 91, 3, 196, 105, 0, 212], "start": 300, "end": 360}, "definition_location": {"file_hash": [7, 104, 78, 160, 210, 177, 46, 35, 129, 112, 10, 106, 124, 79, 141, 238, 66, 236, 155, 211, 143, 140, 114, 87, 115, 74, 91, 3, 196, 105, 0, 212], "start": 318, "end": 337}, "type_parameters": [["T", {"file_hash": [7, 104, 78, 160, 210, 177, 46, 35, 129, 112, 10, 106, 124, 79, 141, 238, 66, 236, 155, 211, 143, 140, 114, 87, 115, 74, 91, 3, 196, 105, 0, 212], "start": 338, "end": 339}]], "parameters": [["_#0#0", {"file_hash": [7, 104, 78, 160, 210, 177, 46, 35, 129, 112, 10, 106, 124, 79, 141, 238, 66, 236, 155, 211, 143, 140, 114, 87, 115, 74, 91, 3, 196, 105, 0, 212], "start": 347, "end": 348}]], "returns": [{"file_hash": [7, 104, 78, 160, 210, 177, 46, 35, 129, 112, 10, 106, 124, 79, 141, 238, 66, 236, 155, 211, 143, 140, 114, 87, 115, 74, 91, 3, 196, 105, 0, 212], "start": 355, "end": 359}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}
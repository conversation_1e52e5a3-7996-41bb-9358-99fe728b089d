import { Injectable, Logger } from '@nestjs/common';
import { SuiService, FileMetadata } from '../sui/sui.service';
import { AuthService } from '../auth/auth.service';
import { isValidSuiAddress, normalizeSuiAddress } from '@mysten/sui/utils';

export interface AccessControlRule {
  conditionType: 'email' | 'wallet' | 'time' | 'hybrid';
  allowedEmails?: string[];
  allowedAddresses?: string[];
  allowedSuiNS?: string[];
  accessStartTime?: number;
  accessEndTime?: number;
  maxAccessDuration?: number;
  requireAllConditions?: boolean;
  maxAccessCount?: number;
}

export interface CreateAccessControlRequest {
  fileCid: string;
  accessRule: AccessControlRule;
}

export interface UpdateAccessControlRequest {
  fileCid: string;
  accessRule: AccessControlRule;
}

export interface ValidateAccessRequest {
  fileCid: string;
  userAddress: string;
  userEmail?: string;
}

export interface AccessControlResponse {
  success: boolean;
  message: string;
  transactionDigest?: string;
  accessGranted?: boolean;
  accessControlId?: string;
}

export interface AccessControlInfo {
  fileCid: string;
  owner: string;
  conditionType: string;
  allowedEmails: string[];
  allowedAddresses: string[];
  accessStartTime?: number;
  accessEndTime?: number;
  requireAllConditions: boolean;
  currentAccessCount: number;
  totalUserRecords: number;
}

@Injectable()
export class AccessControlService {
  private readonly logger = new Logger(AccessControlService.name);

  constructor(
    private readonly suiService: SuiService,
    private readonly authService: AuthService
  ) {}

  /**
   * Create access control rules for a file
   */
  async createAccessControl(
    token: string,
    request: CreateAccessControlRequest
  ): Promise<AccessControlResponse> {
    try {
      // Verify user authentication
      const user = await this.authService.verifyToken(token);
      if (!user) {
        return {
          success: false,
          message: 'Authentication failed',
        };
      }

      this.logger.log(`Creating access control for file ${request.fileCid} by ${user.zkLoginAddress}`);

      // Validate access rule
      const validationResult = this.validateAccessRule(request.accessRule);
      if (!validationResult.valid) {
        return {
          success: false,
          message: `Invalid access rule: ${validationResult.error}`,
        };
      }

      // Create access control on smart contract
      const transactionDigest = await this.suiService.createFileAccessControl(
        user.zkLoginAddress,
        request.fileCid,
        request.accessRule
      );

      this.logger.log(`Access control created for file ${request.fileCid}: ${transactionDigest}`);

      return {
        success: true,
        message: 'Access control created successfully',
        transactionDigest,
      };
    } catch (error) {
      this.logger.error('Failed to create access control', error);
      return {
        success: false,
        message: `Failed to create access control: ${error.message}`,
      };
    }
  }

  /**
   * Update access control rules for a file
   */
  async updateAccessControl(
    token: string,
    request: UpdateAccessControlRequest
  ): Promise<AccessControlResponse> {
    try {
      // Verify user authentication
      const user = await this.authService.verifyToken(token);
      if (!user) {
        return {
          success: false,
          message: 'Authentication failed',
        };
      }

      this.logger.log(`Updating access control for file ${request.fileCid} by ${user.zkLoginAddress}`);

      // Validate access rule
      const validationResult = this.validateAccessRule(request.accessRule);
      if (!validationResult.valid) {
        return {
          success: false,
          message: `Invalid access rule: ${validationResult.error}`,
        };
      }

      // Update access control on smart contract
      const transactionDigest = await this.suiService.updateFileAccessControl(
        user.zkLoginAddress,
        request.fileCid,
        request.accessRule
      );

      this.logger.log(`Access control updated for file ${request.fileCid}: ${transactionDigest}`);

      return {
        success: true,
        message: 'Access control updated successfully',
        transactionDigest,
      };
    } catch (error) {
      this.logger.error('Failed to update access control', error);
      return {
        success: false,
        message: `Failed to update access control: ${error.message}`,
      };
    }
  }

  /**
   * Validate if a user has access to a file
   */
  async validateAccess(
    token: string,
    request: ValidateAccessRequest
  ): Promise<AccessControlResponse> {
    try {
      // Verify user authentication
      const user = await this.authService.verifyToken(token);
      if (!user) {
        return {
          success: false,
          message: 'Authentication failed',
          accessGranted: false,
        };
      }

      this.logger.log(`Validating access for file ${request.fileCid} by ${user.zkLoginAddress}`);

      // Check access through smart contract
      const accessGranted = await this.suiService.validateFileAccess(
        request.fileCid,
        request.userAddress || user.zkLoginAddress,
        request.userEmail || user.email
      );

      this.logger.log(`Access validation result for file ${request.fileCid}: ${accessGranted}`);

      return {
        success: true,
        message: accessGranted ? 'Access granted' : 'Access denied',
        accessGranted,
      };
    } catch (error) {
      this.logger.error('Failed to validate access', error);
      return {
        success: false,
        message: `Failed to validate access: ${error.message}`,
        accessGranted: false,
      };
    }
  }

  /**
   * Get access control information for a file
   */
  async getAccessControlInfo(
    token: string,
    fileCid: string
  ): Promise<{ success: boolean; data?: AccessControlInfo; message: string }> {
    try {
      // Verify user authentication
      const user = await this.authService.verifyToken(token);
      if (!user) {
        return {
          success: false,
          message: 'Authentication failed',
        };
      }

      this.logger.log(`Getting access control info for file ${fileCid}`);

      // Get access control info from smart contract
      const accessControlInfo = await this.suiService.getFileAccessControlInfo(fileCid);

      if (!accessControlInfo) {
        return {
          success: false,
          message: 'Access control not found for this file',
        };
      }

      return {
        success: true,
        data: accessControlInfo,
        message: 'Access control information retrieved successfully',
      };
    } catch (error) {
      this.logger.error('Failed to get access control info', error);
      return {
        success: false,
        message: `Failed to get access control info: ${error.message}`,
      };
    }
  }

  /**
   * Validate access rule structure and constraints
   */
  private validateAccessRule(rule: AccessControlRule): { valid: boolean; error?: string } {
    // Check condition type
    if (!['email', 'wallet', 'time', 'hybrid'].includes(rule.conditionType)) {
      return { valid: false, error: 'Invalid condition type' };
    }

    // Validate email addresses
    if (rule.allowedEmails && rule.allowedEmails.length > 0) {
      for (const email of rule.allowedEmails) {
        if (!this.isValidEmail(email)) {
          return { valid: false, error: `Invalid email address: ${email}` };
        }
      }
    }

    // Validate wallet addresses
    if (rule.allowedAddresses && rule.allowedAddresses.length > 0) {
      for (let i = 0; i < rule.allowedAddresses.length; i++) {
        const address = rule.allowedAddresses[i];
        if (!isValidSuiAddress(address)) {
          return { valid: false, error: `Invalid Sui address: ${address}` };
        }
        // Normalize the address to full format
        rule.allowedAddresses[i] = normalizeSuiAddress(address);
      }
    }

    // Validate SuiNS names
    if (rule.allowedSuiNS && rule.allowedSuiNS.length > 0) {
      for (const suiNS of rule.allowedSuiNS) {
        if (!this.isValidSuiNS(suiNS)) {
          return { valid: false, error: `Invalid SuiNS name: ${suiNS}` };
        }
      }
    }

    // Validate time constraints
    if (rule.accessStartTime && rule.accessEndTime) {
      if (rule.accessStartTime >= rule.accessEndTime) {
        return { valid: false, error: 'Access start time must be before end time' };
      }
    }

    if (rule.maxAccessDuration && rule.maxAccessDuration <= 0) {
      return { valid: false, error: 'Max access duration must be positive' };
    }

    if (rule.maxAccessCount && rule.maxAccessCount <= 0) {
      return { valid: false, error: 'Max access count must be positive' };
    }

    // Ensure at least one access method is specified for hybrid type
    if (rule.conditionType === 'hybrid') {
      const hasEmail = rule.allowedEmails && rule.allowedEmails.length > 0;
      const hasAddress = rule.allowedAddresses && rule.allowedAddresses.length > 0;
      const hasSuiNS = rule.allowedSuiNS && rule.allowedSuiNS.length > 0;
      const hasTime = rule.accessStartTime || rule.accessEndTime || rule.maxAccessDuration;

      if (!hasEmail && !hasAddress && !hasSuiNS && !hasTime) {
        return { valid: false, error: 'Hybrid access control must specify at least one access method' };
      }
    }

    return { valid: true };
  }

  /**
   * Validate email address format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }



  /**
   * Resolve email to wallet address (if user has linked accounts)
   */
  async resolveEmailToAddress(email: string): Promise<string | null> {
    try {
      // This would integrate with your user database to find linked wallet addresses
      // For now, return null as this requires additional user management infrastructure
      this.logger.log(`Email to address resolution not implemented for: ${email}`);
      return null;
    } catch (error) {
      this.logger.error('Failed to resolve email to address', error);
      return null;
    }
  }

  /**
   * Generate a shareable link for a file
   */
  async generateShareLink(
    token: string,
    request: { fileCid: string; expirationTime?: number; maxUses?: number }
  ): Promise<{ success: boolean; data?: any; message: string }> {
    try {
      // Verify user authentication
      const user = await this.authService.verifyToken(token);
      if (!user) {
        return {
          success: false,
          message: 'Authentication failed',
        };
      }

      // Check if user owns the file or has permission to create share links
      // This would typically check the smart contract for file ownership
      this.logger.log(`Generating share link for file ${request.fileCid} by ${user.zkLoginAddress}`);

      // Generate unique share ID
      const shareId = `share_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Create share link URL
      const shareLink = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/share/${shareId}`;

      // Store share link data (in a real implementation, this would be stored in a database)
      const shareData = {
        shareId,
        fileCid: request.fileCid,
        createdBy: user.zkLoginAddress,
        createdAt: Date.now(),
        expirationTime: request.expirationTime,
        maxUses: request.maxUses,
        currentUses: 0,
      };

      this.logger.log(`Share link created: ${shareId} for file ${request.fileCid}`);

      return {
        success: true,
        data: {
          shareLink,
          shareId,
          expirationTime: request.expirationTime,
          maxUses: request.maxUses,
        },
        message: 'Share link generated successfully',
      };
    } catch (error) {
      this.logger.error('Failed to generate share link', error);
      return {
        success: false,
        message: `Failed to generate share link: ${error.message}`,
      };
    }
  }

  /**
   * Generate a shareable link for a file (test mode)
   */
  async generateShareLinkTest(
    request: { fileCid: string; expirationTime?: number; maxUses?: number }
  ): Promise<{ success: boolean; data?: any; message: string }> {
    try {
      this.logger.log(`Generating share link (test mode) for file ${request.fileCid}`);

      // Generate unique share ID
      const shareId = `test_share_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Create share link URL
      const shareLink = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/share/${shareId}`;

      return {
        success: true,
        data: {
          shareLink,
          shareId,
          expirationTime: request.expirationTime,
          maxUses: request.maxUses,
        },
        message: 'Share link generated successfully (test mode)',
      };
    } catch (error) {
      this.logger.error('Failed to generate share link (test)', error);
      return {
        success: false,
        message: `Failed to generate share link: ${error.message}`,
      };
    }
  }

  /**
   * Validate a share link and return file access information
   */
  async validateShareLink(
    shareId: string,
    token?: string
  ): Promise<{ success: boolean; data?: any; message: string }> {
    try {
      this.logger.log(`Validating share link: ${shareId}`);

      // Check if share link exists and is valid
      if (!shareId.startsWith('share_') && !shareId.startsWith('test_share_')) {
        return {
          success: false,
          message: 'Invalid share link',
        };
      }

      // In a real implementation, this would look up the share link in a database
      // For now, we'll simulate validation and extract file CID from share ID
      let fileCid: string;

      if (shareId.startsWith('test_share_')) {
        // For test shares, extract from the share ID pattern
        const parts = shareId.split('_');
        if (parts.length >= 3) {
          fileCid = `file_${parts[2]}`;
        } else {
          fileCid = 'test_file_cid';
        }
      } else {
        // For regular shares, extract from the share ID pattern
        const parts = shareId.split('_');
        if (parts.length >= 3) {
          fileCid = `file_${parts[2]}`;
        } else {
          return {
            success: false,
            message: 'Invalid share link format',
          };
        }
      }

      // Simulate share link data (in real implementation, this would be stored in database)
      const shareData = {
        shareId,
        fileCid,
        createdAt: Date.now() - 3600000, // 1 hour ago
        expirationTime: Date.now() + 86400000, // 24 hours from now
        maxUses: 10,
        currentUses: 1,
      };

      // Check expiration
      if (shareData.expirationTime && Date.now() > shareData.expirationTime) {
        return {
          success: false,
          message: 'Share link has expired',
        };
      }

      // Check usage limit
      if (shareData.maxUses && shareData.currentUses >= shareData.maxUses) {
        return {
          success: false,
          message: 'Share link usage limit exceeded',
        };
      }

      // Try to get file metadata from the blockchain
      let fileMetadata: FileMetadata | null = null;
      try {
        fileMetadata = await this.suiService.getFileMetadata(fileCid);
      } catch (error) {
        this.logger.warn(`Could not retrieve file metadata for ${fileCid}:`, error);
      }

      // Increment usage count (in real implementation, this would be persisted)
      shareData.currentUses++;

      return {
        success: true,
        data: {
          fileCid: shareData.fileCid,
          accessGranted: true,
          filename: fileMetadata?.filename || 'shared-file',
          fileSize: fileMetadata?.fileSize,
          contentType: 'application/octet-stream', // Default content type
          isEncrypted: false, // This would be determined from file metadata or storage
        },
        message: 'Share link validated successfully',
      };
    } catch (error) {
      this.logger.error('Failed to validate share link', error);
      return {
        success: false,
        message: `Failed to validate share link: ${error.message}`,
      };
    }
  }

  /**
   * Process bulk upload data from CSV/Excel files
   */
  async processBulkUpload(
    token: string,
    file: Express.Multer.File,
    fileCid: string,
    conditionType: 'email' | 'wallet' | 'hybrid'
  ): Promise<{ success: boolean; data?: any; message: string }> {
    try {
      // Verify user authentication
      const user = await this.authService.verifyToken(token);
      if (!user) {
        return {
          success: false,
          message: 'Authentication failed',
        };
      }

      this.logger.log(`Processing bulk upload for file ${fileCid} by ${user.zkLoginAddress}`);

      // Validate file type
      const allowedMimeTypes = [
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];

      if (!allowedMimeTypes.includes(file.mimetype)) {
        return {
          success: false,
          message: 'Invalid file type. Only CSV and Excel files are supported.',
        };
      }

      // Parse file content
      const fileContent = file.buffer.toString('utf-8');
      const parsedData = this.parseBulkData(fileContent, file.mimetype);

      if (parsedData.errors.length > 0) {
        return {
          success: false,
          message: `Validation errors: ${parsedData.errors.join(', ')}`,
          data: { errors: parsedData.errors },
        };
      }

      // Create access control rule from parsed data
      const accessRule: AccessControlRule = {
        conditionType,
        allowedEmails: parsedData.emails.length > 0 ? parsedData.emails : undefined,
        allowedAddresses: parsedData.addresses.length > 0 ? parsedData.addresses : undefined,
        allowedSuiNS: parsedData.suiNSNames.length > 0 ? parsedData.suiNSNames : undefined,
      };

      // Validate the access rule
      const validation = this.validateAccessRule(accessRule);
      if (!validation.valid) {
        return {
          success: false,
          message: validation.error || 'Invalid access rule',
        };
      }

      // Update access control with bulk data
      const updateResult = await this.updateAccessControl(token, {
        fileCid,
        accessRule,
      });

      if (!updateResult.success) {
        return {
          success: false,
          message: updateResult.message,
        };
      }

      return {
        success: true,
        data: {
          processed: {
            emails: parsedData.emails.length,
            addresses: parsedData.addresses.length,
            suiNSNames: parsedData.suiNSNames.length,
          },
          transactionDigest: updateResult.transactionDigest,
        },
        message: `Bulk upload processed successfully. Added ${parsedData.emails.length} emails, ${parsedData.addresses.length} addresses, and ${parsedData.suiNSNames.length} SuiNS names.`,
      };
    } catch (error) {
      this.logger.error('Failed to process bulk upload', error);
      return {
        success: false,
        message: `Failed to process bulk upload: ${error.message}`,
      };
    }
  }

  /**
   * Parse bulk data from file content
   */
  private parseBulkData(
    content: string,
    mimeType: string
  ): { emails: string[]; addresses: string[]; suiNSNames: string[]; errors: string[] } {
    const result = {
      emails: [] as string[],
      addresses: [] as string[],
      suiNSNames: [] as string[],
      errors: [] as string[],
    };

    try {
      let lines: string[] = [];

      if (mimeType === 'text/csv') {
        // Parse CSV
        lines = content.split('\n').map(line => line.trim()).filter(line => line);
      } else {
        // For Excel files, we'd need a proper Excel parser
        // For now, treat as CSV (this would need xlsx library in production)
        lines = content.split('\n').map(line => line.trim()).filter(line => line);
      }

      // Skip header row if it exists
      const hasHeader = lines.length > 0 && (
        lines[0].toLowerCase().includes('email') ||
        lines[0].toLowerCase().includes('address') ||
        lines[0].toLowerCase().includes('suins')
      );

      const dataLines = hasHeader ? lines.slice(1) : lines;

      for (const line of dataLines) {
        if (!line) continue;

        // Split by comma for CSV
        const values = line.split(',').map(v => v.trim().replace(/"/g, ''));

        for (const value of values) {
          if (!value) continue;

          // Determine type and validate
          if (this.isValidEmail(value)) {
            if (!result.emails.includes(value)) {
              result.emails.push(value);
            }
          } else if (isValidSuiAddress(value)) {
            if (!result.addresses.includes(value)) {
              result.addresses.push(value);
            }
          } else if (this.isValidSuiNS(value)) {
            if (!result.suiNSNames.includes(value)) {
              result.suiNSNames.push(value);
            }
          } else {
            result.errors.push(`Invalid format: ${value}`);
          }
        }
      }

      // Limit the number of entries to prevent abuse
      const maxEntries = 1000;
      if (result.emails.length > maxEntries) {
        result.errors.push(`Too many emails (max ${maxEntries})`);
        result.emails = result.emails.slice(0, maxEntries);
      }
      if (result.addresses.length > maxEntries) {
        result.errors.push(`Too many addresses (max ${maxEntries})`);
        result.addresses = result.addresses.slice(0, maxEntries);
      }
      if (result.suiNSNames.length > maxEntries) {
        result.errors.push(`Too many SuiNS names (max ${maxEntries})`);
        result.suiNSNames = result.suiNSNames.slice(0, maxEntries);
      }

    } catch (error) {
      result.errors.push(`Failed to parse file: ${error.message}`);
    }

    return result;
  }

  /**
   * Validate SuiNS name format
   */
  private isValidSuiNS(name: string): boolean {
    // SuiNS names should end with .sui and contain valid characters
    const suiNSRegex = /^[a-zA-Z0-9-_]+\.sui$/;
    return suiNSRegex.test(name);
  }
}

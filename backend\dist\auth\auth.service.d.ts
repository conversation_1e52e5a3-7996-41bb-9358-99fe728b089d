import { ZkLoginService, AuthenticatedUser, ZkLoginSession } from './zklogin.service';
import { SuiService } from '../sui/sui.service';
export interface SessionToken {
    sessionId: string;
    zkLoginAddress: string;
    provider: string;
    email?: string;
    name?: string;
    iat: number;
    exp: number;
}
export interface AuthSession {
    id: string;
    zkLoginSession: ZkLoginSession;
    user?: AuthenticatedUser;
    createdAt: Date;
    expiresAt: Date;
}
export declare class AuthService {
    private readonly zkLoginService;
    private readonly suiService;
    private readonly logger;
    private readonly sessions;
    private readonly config;
    constructor(zkLoginService: ZkLoginService, suiService: SuiService);
    createSession(provider: string): Promise<{
        sessionId: string;
        authUrl: string;
    }>;
    completeAuthentication(sessionId: string, code: string, state?: string): Promise<{
        token: string;
        user: AuthenticatedUser;
    }>;
    verifyToken(token: string): Promise<AuthenticatedUser | null>;
    getSession(sessionId: string): AuthSession | undefined;
    revokeSession(sessionId: string): boolean;
    private generateSessionId;
    private generateSessionToken;
    private cleanupExpiredSessions;
    getUserZkLoginAddress(token: string): Promise<string | null>;
    isUserAuthorizedForFile(token: string, fileCid: string): Promise<boolean>;
}

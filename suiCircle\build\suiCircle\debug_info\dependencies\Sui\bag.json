{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\bag.move", "definition_location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1174, "end": 1177}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "bag"], "struct_map": {"0": {"definition_location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1305, "end": 1308}, "type_parameters": [], "fields": [{"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1359, "end": 1361}, {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1423, "end": 1427}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1470, "end": 1582}, "definition_location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1481, "end": 1484}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1485, "end": 1488}]], "returns": [{"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1507, "end": 1510}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1549, "end": 1552}, "1": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1537, "end": 1553}, "2": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1570, "end": 1571}, "3": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1518, "end": 1579}}, "is_native": false}, "1": {"location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1758, "end": 1904}, "definition_location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1769, "end": 1772}, "type_parameters": [["K", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1773, "end": 1774}], ["V", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1797, "end": 1798}]], "parameters": [["bag#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1807, "end": 1810}], ["k#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1822, "end": 1823}], ["v#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1828, "end": 1829}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1857, "end": 1860}, "1": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1852, "end": 1863}, "2": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1865, "end": 1866}, "3": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1868, "end": 1869}, "4": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1841, "end": 1870}, "5": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1888, "end": 1891}, "6": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1888, "end": 1896}, "8": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1899, "end": 1900}, "9": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1897, "end": 1898}, "10": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1877, "end": 1880}, "11": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1877, "end": 1885}, "12": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1877, "end": 1900}, "13": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 1900, "end": 1901}}, "is_native": false}, "2": {"location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2274, "end": 2382}, "definition_location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2285, "end": 2291}, "type_parameters": [["K", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2292, "end": 2293}], ["V", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2316, "end": 2317}]], "parameters": [["bag#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2326, "end": 2329}], ["k#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2337, "end": 2338}]], "returns": [{"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2344, "end": 2346}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2369, "end": 2372}, "1": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2368, "end": 2375}, "2": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2377, "end": 2378}, "3": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2354, "end": 2379}}, "is_native": false}, "3": {"location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2754, "end": 2882}, "definition_location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2765, "end": 2775}, "type_parameters": [["K", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2776, "end": 2777}], ["V", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2800, "end": 2801}]], "parameters": [["bag#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2810, "end": 2813}], ["k#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2825, "end": 2826}]], "returns": [{"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2832, "end": 2838}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2869, "end": 2872}, "1": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2864, "end": 2875}, "2": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2877, "end": 2878}, "3": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 2846, "end": 2879}}, "is_native": false}, "4": {"location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3243, "end": 3404}, "definition_location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3254, "end": 3260}, "type_parameters": [["K", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3261, "end": 3262}], ["V", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3285, "end": 3286}]], "parameters": [["bag#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3295, "end": 3298}], ["k#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3310, "end": 3311}]], "returns": [{"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3317, "end": 3318}], "locals": [["v#1#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3330, "end": 3331}]], "nops": {}, "code_map": {"0": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3353, "end": 3356}, "1": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3348, "end": 3359}, "2": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3361, "end": 3362}, "3": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3334, "end": 3363}, "4": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3330, "end": 3331}, "5": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3381, "end": 3384}, "6": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3381, "end": 3389}, "8": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3392, "end": 3393}, "9": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3390, "end": 3391}, "10": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3370, "end": 3373}, "11": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3370, "end": 3378}, "12": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3370, "end": 3393}, "13": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3400, "end": 3401}}, "is_native": false}, "5": {"location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3502, "end": 3608}, "definition_location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3513, "end": 3521}, "type_parameters": [["K", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3522, "end": 3523}]], "parameters": [["bag#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3546, "end": 3549}], ["k#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3557, "end": 3558}]], "returns": [{"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3564, "end": 3568}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3595, "end": 3598}, "1": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3594, "end": 3601}, "2": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3603, "end": 3604}, "3": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3576, "end": 3605}}, "is_native": false}, "6": {"location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3746, "end": 3884}, "definition_location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3757, "end": 3775}, "type_parameters": [["K", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3776, "end": 3777}], ["V", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3800, "end": 3801}]], "parameters": [["bag#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3810, "end": 3813}], ["k#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3821, "end": 3822}]], "returns": [{"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3828, "end": 3832}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3871, "end": 3874}, "1": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3870, "end": 3877}, "2": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3879, "end": 3880}, "3": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3840, "end": 3881}}, "is_native": false}, "7": {"location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3952, "end": 4004}, "definition_location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3963, "end": 3969}, "type_parameters": [], "parameters": [["bag#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3970, "end": 3973}]], "returns": [{"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3982, "end": 3985}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3993, "end": 3996}, "1": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 3993, "end": 4001}}, "is_native": false}, "8": {"location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4073, "end": 4133}, "definition_location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4084, "end": 4092}, "type_parameters": [], "parameters": [["bag#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4093, "end": 4096}]], "returns": [{"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4105, "end": 4109}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4117, "end": 4120}, "1": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4117, "end": 4125}, "3": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4129, "end": 4130}, "4": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4126, "end": 4128}, "5": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4117, "end": 4130}}, "is_native": false}, "9": {"location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4229, "end": 4357}, "definition_location": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4240, "end": 4253}, "type_parameters": [], "parameters": [["bag#0#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4254, "end": 4257}]], "returns": [], "locals": [["id#1#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4281, "end": 4283}], ["size#1#0", {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4285, "end": 4289}]], "nops": {}, "code_map": {"0": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4294, "end": 4297}, "1": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4275, "end": 4291}, "2": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4285, "end": 4289}, "3": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4281, "end": 4283}, "4": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4312, "end": 4316}, "5": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4320, "end": 4321}, "6": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4317, "end": 4319}, "7": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4304, "end": 4336}, "9": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4323, "end": 4335}, "10": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4304, "end": 4336}, "11": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4343, "end": 4345}, "12": {"file_hash": [155, 56, 10, 25, 152, 142, 20, 134, 237, 54, 88, 169, 58, 162, 69, 203, 112, 102, 183, 29, 23, 115, 175, 230, 119, 128, 129, 161, 178, 163, 203, 212], "start": 4343, "end": 4354}}, "is_native": false}}, "constant_map": {"EBagNotEmpty": 0}}
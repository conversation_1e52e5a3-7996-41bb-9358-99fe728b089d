{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\math.move", "definition_location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 275, "end": 279}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "math"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 329, "end": 383}, "definition_location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 340, "end": 343}, "type_parameters": [], "parameters": [["x#0#0", {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 344, "end": 345}], ["y#0#0", {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 352, "end": 353}]], "returns": [{"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 361, "end": 364}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 372, "end": 373}, "1": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 378, "end": 379}, "2": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 372, "end": 380}}, "is_native": false}, "1": {"location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 432, "end": 486}, "definition_location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 443, "end": 446}, "type_parameters": [], "parameters": [["x#0#0", {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 447, "end": 448}], ["y#0#0", {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 455, "end": 456}]], "returns": [{"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 464, "end": 467}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 475, "end": 476}, "1": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 481, "end": 482}, "2": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 475, "end": 483}}, "is_native": false}, "2": {"location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 536, "end": 592}, "definition_location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 547, "end": 551}, "type_parameters": [], "parameters": [["x#0#0", {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 552, "end": 553}], ["y#0#0", {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 560, "end": 561}]], "returns": [{"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 569, "end": 572}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 580, "end": 581}, "1": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 587, "end": 588}, "2": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 580, "end": 589}}, "is_native": false}, "3": {"location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 641, "end": 714}, "definition_location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 652, "end": 655}, "type_parameters": [], "parameters": [["base#0#0", {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 656, "end": 660}], ["exponent#0#0", {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 667, "end": 675}]], "returns": [{"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 682, "end": 685}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 693, "end": 697}, "1": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 702, "end": 710}, "2": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 693, "end": 711}}, "is_native": false}, "4": {"location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 764, "end": 811}, "definition_location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 775, "end": 779}, "type_parameters": [], "parameters": [["x#0#0", {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 780, "end": 781}]], "returns": [{"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 789, "end": 792}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 800, "end": 801}, "1": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 800, "end": 808}}, "is_native": false}, "5": {"location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 862, "end": 916}, "definition_location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 873, "end": 882}, "type_parameters": [], "parameters": [["x#0#0", {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 883, "end": 884}]], "returns": [{"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 893, "end": 897}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 905, "end": 906}, "1": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 905, "end": 913}}, "is_native": false}, "6": {"location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 981, "end": 1067}, "definition_location": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 992, "end": 1011}, "type_parameters": [], "parameters": [["x#0#0", {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 1012, "end": 1013}], ["y#0#0", {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 1020, "end": 1021}]], "returns": [{"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 1029, "end": 1032}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 1040, "end": 1041}, "1": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 1062, "end": 1063}, "2": {"file_hash": [249, 51, 193, 163, 169, 126, 55, 192, 112, 196, 41, 187, 128, 111, 21, 26, 61, 204, 230, 149, 245, 190, 154, 54, 40, 163, 49, 121, 235, 52, 175, 194], "start": 1040, "end": 1064}}, "is_native": false}}, "constant_map": {}}
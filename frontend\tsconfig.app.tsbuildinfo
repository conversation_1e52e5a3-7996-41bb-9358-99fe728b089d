{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/filelist.tsx", "./src/components/fileupload.tsx", "./src/components/userprofile.tsx", "./src/components/walletdisplay.tsx", "./src/components/zkloginauth.tsx", "./src/components/features/filelist.tsx", "./src/components/icons/socialicons.tsx", "./src/components/layout/header.tsx", "./src/components/pages/authpage.tsx", "./src/components/pages/encryptiontestpage.tsx", "./src/components/pages/filelistpage.tsx", "./src/components/pages/landingpage.tsx", "./src/components/pages/transferpage.tsx", "./src/components/ui/accordion.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/aspect-ratio.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/breadcrumb.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/context-menu.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/menubar.tsx", "./src/components/ui/navigation-menu.tsx", "./src/components/ui/pagination.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/contexts/authcontext.tsx", "./src/hooks/use-toast.ts", "./src/hooks/usefileupload.ts", "./src/hooks/usesealencryption.ts", "./src/lib/utils.ts", "./src/services/authservice.ts", "./src/services/fileservice.ts", "./src/services/sealencryptionservice.ts", "./src/types/index.ts", "./src/utils/encryptiontestutils.ts"], "errors": true, "version": "5.6.3"}
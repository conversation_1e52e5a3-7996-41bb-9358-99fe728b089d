"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jsonwebtoken_1 = require("jsonwebtoken");
const zklogin_service_1 = require("./zklogin.service");
const zklogin_config_1 = require("../config/zklogin.config");
const sui_service_1 = require("../sui/sui.service");
let AuthService = AuthService_1 = class AuthService {
    zkLoginService;
    suiService;
    logger = new common_1.Logger(AuthService_1.name);
    sessions = new Map();
    config = zklogin_config_1.defaultZkLoginConfig;
    constructor(zkLoginService, suiService) {
        this.zkLoginService = zkLoginService;
        this.suiService = suiService;
    }
    async createSession(provider) {
        try {
            const { authUrl, session } = await this.zkLoginService.createZkLoginSession(provider);
            const sessionId = this.generateSessionId();
            const authSession = {
                id: sessionId,
                zkLoginSession: session,
                createdAt: new Date(),
                expiresAt: new Date(Date.now() + 30 * 60 * 1000),
            };
            this.sessions.set(sessionId, authSession);
            this.cleanupExpiredSessions();
            return { sessionId, authUrl };
        }
        catch (error) {
            this.logger.error('Failed to create authentication session', error);
            throw new Error('Failed to create authentication session');
        }
    }
    async completeAuthentication(sessionId, code, state) {
        try {
            this.logger.log(`Starting authentication completion for session: ${sessionId}`);
            const session = this.sessions.get(sessionId);
            if (!session) {
                this.logger.error(`Session not found: ${sessionId}`);
                throw new Error('Invalid or expired session');
            }
            if (session.expiresAt < new Date()) {
                this.logger.error(`Session expired: ${sessionId}`);
                this.sessions.delete(sessionId);
                throw new Error('Session expired');
            }
            this.logger.log(`Session found, exchanging code for JWT...`);
            const jwt = await this.zkLoginService.exchangeCodeForToken(session.zkLoginSession.provider, code);
            this.logger.log(`JWT obtained, completing zkLogin authentication...`);
            const user = await this.zkLoginService.completeAuthentication(session.zkLoginSession, jwt);
            this.logger.log(`zkLogin authentication completed for user: ${user.zkLoginAddress}`);
            session.user = user;
            const token = this.generateSessionToken(sessionId, user);
            this.logger.log(`Session token generated successfully`);
            return { token, user };
        }
        catch (error) {
            this.logger.error('Failed to complete authentication', error);
            this.logger.error('Error stack:', error.stack);
            throw new Error('Failed to complete authentication');
        }
    }
    async verifyToken(token) {
        try {
            const decoded = (0, jsonwebtoken_1.verify)(token, this.config.jwt.secret);
            const session = this.sessions.get(decoded.sessionId);
            if (!session || !session.user) {
                return null;
            }
            if (session.expiresAt < new Date()) {
                this.sessions.delete(decoded.sessionId);
                return null;
            }
            return session.user;
        }
        catch (error) {
            this.logger.error('Failed to verify token', error);
            return null;
        }
    }
    getSession(sessionId) {
        return this.sessions.get(sessionId);
    }
    revokeSession(sessionId) {
        return this.sessions.delete(sessionId);
    }
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    generateSessionToken(sessionId, user) {
        const payload = {
            sessionId,
            zkLoginAddress: user.zkLoginAddress,
            provider: user.provider,
            email: user.email,
            name: user.name,
        };
        return (0, jsonwebtoken_1.sign)(payload, this.config.jwt.secret, {
            expiresIn: this.config.jwt.expiresIn,
        });
    }
    cleanupExpiredSessions() {
        const now = new Date();
        for (const [sessionId, session] of this.sessions.entries()) {
            if (session.expiresAt < now) {
                this.sessions.delete(sessionId);
            }
        }
    }
    async getUserZkLoginAddress(token) {
        const user = await this.verifyToken(token);
        return user?.zkLoginAddress || null;
    }
    async isUserAuthorizedForFile(token, fileCid) {
        try {
            const user = await this.verifyToken(token);
            if (!user) {
                return false;
            }
            const isAuthorized = await this.suiService.isAuthorizedForFile(fileCid, user.zkLoginAddress);
            this.logger.log(`Authorization check for user ${user.zkLoginAddress} and file ${fileCid}: ${isAuthorized}`);
            return isAuthorized;
        }
        catch (error) {
            this.logger.error('Failed to check user authorization', error);
            return false;
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => sui_service_1.SuiService))),
    __metadata("design:paramtypes", [zklogin_service_1.ZkLoginService,
        sui_service_1.SuiService])
], AuthService);
//# sourceMappingURL=auth.service.js.map
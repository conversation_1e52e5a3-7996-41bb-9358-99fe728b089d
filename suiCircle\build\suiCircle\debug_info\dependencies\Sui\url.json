{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\url.move", "definition_location": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 141, "end": 144}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "url"], "struct_map": {"0": {"definition_location": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 243, "end": 246}, "type_parameters": [], "fields": [{"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 310, "end": 313}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 369, "end": 430}, "definition_location": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 380, "end": 390}, "type_parameters": [], "parameters": [["url#0#0", {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 391, "end": 394}]], "returns": [{"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 405, "end": 408}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 422, "end": 425}, "1": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 416, "end": 427}}, "is_native": false}, "1": {"location": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 541, "end": 659}, "definition_location": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 552, "end": 573}, "type_parameters": [], "parameters": [["bytes#0#0", {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 574, "end": 579}]], "returns": [{"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 594, "end": 597}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 615, "end": 620}, "1": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 615, "end": 638}, "2": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 645, "end": 656}}, "is_native": false}, "2": {"location": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 682, "end": 741}, "definition_location": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 693, "end": 702}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 703, "end": 707}]], "returns": [{"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 716, "end": 722}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 730, "end": 734}, "1": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 730, "end": 738}}, "is_native": false}, "3": {"location": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 771, "end": 843}, "definition_location": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 782, "end": 788}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 789, "end": 793}], ["url#0#0", {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 805, "end": 808}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 836, "end": 839}, "1": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 825, "end": 829}, "2": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 825, "end": 833}, "3": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 825, "end": 839}, "4": {"file_hash": [200, 228, 193, 243, 141, 228, 152, 92, 246, 75, 58, 127, 232, 7, 241, 146, 189, 7, 176, 244, 126, 10, 101, 82, 12, 202, 172, 113, 203, 212, 49, 27], "start": 839, "end": 840}}, "is_native": false}}, "constant_map": {}}
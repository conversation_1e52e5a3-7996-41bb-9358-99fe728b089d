"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AccessControlController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccessControlController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const access_control_service_1 = require("./access-control.service");
const auth_guard_1 = require("../auth/auth.guard");
let AccessControlController = AccessControlController_1 = class AccessControlController {
    accessControlService;
    logger = new common_1.Logger(AccessControlController_1.name);
    constructor(accessControlService) {
        this.accessControlService = accessControlService;
    }
    async createAccessControl(authorization, request, user) {
        try {
            const token = authorization.substring(7);
            const result = await this.accessControlService.createAccessControl(token, request);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            return {
                success: true,
                data: {
                    transactionDigest: result.transactionDigest,
                },
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to create access control', error);
            throw new common_1.HttpException(error.message || 'Failed to create access control', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateAccessControl(authorization, request, user) {
        try {
            const token = authorization.substring(7);
            const result = await this.accessControlService.updateAccessControl(token, request);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            return {
                success: true,
                data: {
                    transactionDigest: result.transactionDigest,
                },
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to update access control', error);
            throw new common_1.HttpException(error.message || 'Failed to update access control', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async validateAccess(authorization, request, user) {
        try {
            const token = authorization.substring(7);
            const result = await this.accessControlService.validateAccess(token, request);
            return {
                success: result.success,
                data: {
                    accessGranted: result.accessGranted,
                },
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to validate access', error);
            throw new common_1.HttpException(error.message || 'Failed to validate access', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAccessControlInfo(fileCid, authorization, user) {
        try {
            const token = authorization.substring(7);
            const result = await this.accessControlService.getAccessControlInfo(token, fileCid);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.NOT_FOUND);
            }
            return {
                success: true,
                data: result.data,
                message: result.message,
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error('Failed to get access control info', error);
            throw new common_1.HttpException('Failed to get access control info', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async checkAccess(fileCid, authorization, user) {
        try {
            const token = authorization.substring(7);
            const userInfo = await this.accessControlService['authService'].verifyToken(token);
            if (!userInfo) {
                throw new common_1.HttpException('Authentication failed', common_1.HttpStatus.UNAUTHORIZED);
            }
            const request = {
                fileCid,
                userAddress: userInfo.zkLoginAddress,
                userEmail: userInfo.email,
            };
            const result = await this.accessControlService.validateAccess(token, request);
            return {
                success: result.success,
                data: {
                    accessGranted: result.accessGranted,
                    userAddress: userInfo.zkLoginAddress,
                    userEmail: userInfo.email,
                },
                message: result.message,
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error('Failed to check access', error);
            throw new common_1.HttpException('Failed to check access', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createAccessControlTest(request) {
        try {
            this.logger.log('Creating access control (test mode)');
            return {
                success: true,
                data: {
                    transactionDigest: `test_tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                },
                message: 'Access control created successfully (test mode)',
            };
        }
        catch (error) {
            this.logger.error('Failed to create access control (test)', error);
            throw new common_1.HttpException(error.message || 'Failed to create access control', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async validateAccessTest(request) {
        try {
            this.logger.log('Validating access (test mode)');
            return {
                success: true,
                data: {
                    accessGranted: true,
                },
                message: 'Access granted (test mode)',
            };
        }
        catch (error) {
            this.logger.error('Failed to validate access (test)', error);
            throw new common_1.HttpException(error.message || 'Failed to validate access', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAccessControlInfoTest(fileCid) {
        try {
            this.logger.log(`Getting access control info (test mode) for file: ${fileCid}`);
            return {
                success: true,
                data: {
                    fileCid,
                    owner: 'test-owner',
                    conditionType: 'hybrid',
                    allowedEmails: ['<EMAIL>'],
                    allowedAddresses: ['0x1234567890abcdef'],
                    accessStartTime: Date.now(),
                    accessEndTime: Date.now() + 86400000,
                    requireAllConditions: false,
                    currentAccessCount: 0,
                    totalUserRecords: 0,
                },
                message: 'Access control information retrieved successfully (test mode)',
            };
        }
        catch (error) {
            this.logger.error('Failed to get access control info (test)', error);
            throw new common_1.HttpException('Failed to get access control info', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async generateShareLink(authorization, request, user) {
        try {
            const token = authorization.substring(7);
            const result = await this.accessControlService.generateShareLink(token, request);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            return {
                success: true,
                data: result.data,
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to generate share link', error);
            throw new common_1.HttpException(error.message || 'Failed to generate share link', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async generateShareLinkTest(request) {
        try {
            const result = await this.accessControlService.generateShareLinkTest(request);
            return {
                success: true,
                data: result.data,
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to generate share link (test)', error);
            throw new common_1.HttpException(error.message || 'Failed to generate share link', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async accessViaShareLink(shareId, token) {
        try {
            const result = await this.accessControlService.validateShareLink(shareId, token);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.FORBIDDEN);
            }
            return {
                success: true,
                data: result.data,
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to access via share link', error);
            throw new common_1.HttpException(error.message || 'Failed to access file', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async processBulkUpload(authorization, file, body, user) {
        try {
            if (!file) {
                throw new common_1.HttpException('No file provided', common_1.HttpStatus.BAD_REQUEST);
            }
            const token = authorization.substring(7);
            const result = await this.accessControlService.processBulkUpload(token, file, body.fileCid, body.conditionType);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            return {
                success: true,
                data: result.data,
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to process bulk upload', error);
            throw new common_1.HttpException(error.message || 'Failed to process bulk upload', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.AccessControlController = AccessControlController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AccessControlController.prototype, "createAccessControl", null);
__decorate([
    (0, common_1.Put)(),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AccessControlController.prototype, "updateAccessControl", null);
__decorate([
    (0, common_1.Post)('validate'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AccessControlController.prototype, "validateAccess", null);
__decorate([
    (0, common_1.Get)(':fileCid'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)('fileCid')),
    __param(1, (0, common_1.Headers)('authorization')),
    __param(2, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], AccessControlController.prototype, "getAccessControlInfo", null);
__decorate([
    (0, common_1.Get)(':fileCid/check'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)('fileCid')),
    __param(1, (0, common_1.Headers)('authorization')),
    __param(2, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], AccessControlController.prototype, "checkAccess", null);
__decorate([
    (0, common_1.Post)('test'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AccessControlController.prototype, "createAccessControlTest", null);
__decorate([
    (0, common_1.Post)('validate-test'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AccessControlController.prototype, "validateAccessTest", null);
__decorate([
    (0, common_1.Get)(':fileCid/test'),
    __param(0, (0, common_1.Param)('fileCid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AccessControlController.prototype, "getAccessControlInfoTest", null);
__decorate([
    (0, common_1.Post)('share-link'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AccessControlController.prototype, "generateShareLink", null);
__decorate([
    (0, common_1.Post)('share-link-test'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AccessControlController.prototype, "generateShareLinkTest", null);
__decorate([
    (0, common_1.Get)('share/:shareId'),
    __param(0, (0, common_1.Param)('shareId')),
    __param(1, (0, common_1.Query)('token')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AccessControlController.prototype, "accessViaShareLink", null);
__decorate([
    (0, common_1.Post)('bulk-upload'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.UploadedFile)()),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object, Object]),
    __metadata("design:returntype", Promise)
], AccessControlController.prototype, "processBulkUpload", null);
exports.AccessControlController = AccessControlController = AccessControlController_1 = __decorate([
    (0, common_1.Controller)('access-control'),
    __metadata("design:paramtypes", [access_control_service_1.AccessControlService])
], AccessControlController);
//# sourceMappingURL=access-control.controller.js.map
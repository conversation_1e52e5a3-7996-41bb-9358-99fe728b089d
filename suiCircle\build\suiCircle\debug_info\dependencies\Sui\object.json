{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\object.move", "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 118, "end": 124}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "object"], "struct_map": {"0": {"definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 2163, "end": 2165}, "type_parameters": [], "fields": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 2527, "end": 2532}]}, "1": {"definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3073, "end": 3076}, "type_parameters": [], "fields": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3094, "end": 3096}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3158, "end": 3236}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3169, "end": 3180}, "type_parameters": [], "parameters": [["id#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3181, "end": 3183}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3191, "end": 3201}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3224, "end": 3226}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3223, "end": 3232}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3209, "end": 3233}}, "is_native": false}, "1": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3288, "end": 3349}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3299, "end": 3312}, "type_parameters": [], "parameters": [["id#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3313, "end": 3315}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3323, "end": 3330}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3338, "end": 3340}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3338, "end": 3346}}, "is_native": false}, "2": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3387, "end": 3479}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3398, "end": 3411}, "type_parameters": [], "parameters": [["bytes#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3412, "end": 3417}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3432, "end": 3434}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3462, "end": 3467}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3442, "end": 3468}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3442, "end": 3476}}, "is_native": false}, "3": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3518, "end": 3587}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3529, "end": 3544}, "type_parameters": [], "parameters": [["bytes#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3545, "end": 3550}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3562, "end": 3564}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3577, "end": 3582}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3572, "end": 3584}}, "is_native": false}, "4": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3757, "end": 3932}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3761, "end": 3777}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3778, "end": 3781}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3796, "end": 3799}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3815, "end": 3818}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3815, "end": 3827}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3831, "end": 3835}, "3": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3828, "end": 3830}, "4": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3807, "end": 3855}, "6": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3837, "end": 3854}, "7": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3807, "end": 3855}, "8": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3893, "end": 3919}, "9": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3881, "end": 3921}, "10": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 3862, "end": 3929}}, "is_native": false}, "5": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4043, "end": 4146}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4063, "end": 4068}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4072, "end": 4075}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4114, "end": 4133}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4102, "end": 4135}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4083, "end": 4143}}, "is_native": false}, "6": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4284, "end": 4408}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4304, "end": 4323}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4327, "end": 4330}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4369, "end": 4395}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4357, "end": 4397}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4338, "end": 4405}}, "is_native": false}, "7": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4521, "end": 4629}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4541, "end": 4557}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4561, "end": 4564}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4603, "end": 4616}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4591, "end": 4618}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4572, "end": 4626}}, "is_native": false}, "8": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4747, "end": 4872}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4767, "end": 4790}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4794, "end": 4797}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4836, "end": 4859}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4824, "end": 4861}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4805, "end": 4869}}, "is_native": false}, "9": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4876, "end": 5015}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4896, "end": 4926}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4930, "end": 4933}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4972, "end": 5002}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4960, "end": 5004}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 4941, "end": 5012}}, "is_native": false}, "10": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5019, "end": 5119}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5039, "end": 5067}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5071, "end": 5078}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5086, "end": 5116}}, "is_native": false}, "11": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5259, "end": 5341}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5263, "end": 5269}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5273, "end": 5276}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5315, "end": 5328}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5303, "end": 5330}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5284, "end": 5338}}, "is_native": false}, "12": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5378, "end": 5435}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5389, "end": 5401}, "type_parameters": [], "parameters": [["uid#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5402, "end": 5405}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5414, "end": 5417}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5426, "end": 5429}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5425, "end": 5432}}, "is_native": false}, "13": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5486, "end": 5541}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5497, "end": 5509}, "type_parameters": [], "parameters": [["uid#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5510, "end": 5513}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5522, "end": 5524}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5532, "end": 5535}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5532, "end": 5538}}, "is_native": false}, "14": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5579, "end": 5664}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5590, "end": 5602}, "type_parameters": [], "parameters": [["uid#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5603, "end": 5606}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5615, "end": 5625}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5648, "end": 5651}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5648, "end": 5660}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5647, "end": 5660}, "3": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5633, "end": 5661}}, "is_native": false}, "15": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5716, "end": 5784}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5727, "end": 5741}, "type_parameters": [], "parameters": [["uid#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5742, "end": 5745}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5754, "end": 5761}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5769, "end": 5772}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5769, "end": 5781}}, "is_native": false}, "16": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5938, "end": 6056}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5949, "end": 5952}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5953, "end": 5956}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5975, "end": 5978}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6017, "end": 6020}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6017, "end": 6043}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6005, "end": 6045}, "3": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 5986, "end": 6053}}, "is_native": false}, "17": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6404, "end": 6499}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6415, "end": 6421}, "type_parameters": [], "parameters": [["id#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6422, "end": 6424}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6469, "end": 6471}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6442, "end": 6466}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6452, "end": 6464}, "3": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6478, "end": 6496}}, "is_native": false}, "18": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6541, "end": 6604}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6552, "end": 6554}, "type_parameters": [["T", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6555, "end": 6556}]], "parameters": [["obj#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6563, "end": 6566}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6573, "end": 6575}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6594, "end": 6597}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6583, "end": 6598}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6583, "end": 6601}}, "is_native": false}, "19": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6649, "end": 6721}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6660, "end": 6669}, "type_parameters": [["T", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6670, "end": 6671}]], "parameters": [["obj#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6678, "end": 6681}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6688, "end": 6691}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6711, "end": 6714}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6700, "end": 6715}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6699, "end": 6718}}, "is_native": false}, "20": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6781, "end": 6874}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6792, "end": 6800}, "type_parameters": [["T", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6801, "end": 6802}]], "parameters": [["obj#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6809, "end": 6812}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6819, "end": 6829}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6863, "end": 6866}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6852, "end": 6867}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6851, "end": 6870}, "3": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6837, "end": 6871}}, "is_native": false}, "21": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6936, "end": 7018}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6947, "end": 6957}, "type_parameters": [["T", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6958, "end": 6959}]], "parameters": [["obj#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6966, "end": 6969}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6976, "end": 6983}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7002, "end": 7005}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6991, "end": 7006}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 6991, "end": 7015}}, "is_native": false}, "22": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7335, "end": 7380}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7346, "end": 7356}, "type_parameters": [["T", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7357, "end": 7358}]], "parameters": [["obj#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7365, "end": 7368}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7375, "end": 7379}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "23": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7457, "end": 7578}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7477, "end": 7494}, "type_parameters": [], "parameters": [["bytes#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7495, "end": 7500}]], "returns": [{"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7512, "end": 7515}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7538, "end": 7543}, "1": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7523, "end": 7544}, "2": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7566, "end": 7571}, "3": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7561, "end": 7573}, "4": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7551, "end": 7575}}, "is_native": false}, "24": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7637, "end": 7673}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7648, "end": 7659}, "type_parameters": [], "parameters": [["id#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7660, "end": 7662}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "25": {"location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7716, "end": 7755}, "definition_location": {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7727, "end": 7741}, "type_parameters": [], "parameters": [["id#0#0", {"file_hash": [45, 7, 243, 42, 16, 156, 229, 191, 105, 219, 101, 8, 67, 166, 37, 36, 81, 103, 38, 253, 165, 132, 64, 35, 5, 252, 178, 175, 48, 171, 174, 21], "start": 7742, "end": 7744}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"ENotSystemAddress": 7, "SUI_ACCUMULATOR_ROOT_OBJECT_ID": 5, "SUI_AUTHENTICATOR_STATE_ID": 2, "SUI_BRIDGE_ID": 6, "SUI_CLOCK_OBJECT_ID": 1, "SUI_DENY_LIST_OBJECT_ID": 4, "SUI_RANDOM_ID": 3, "SUI_SYSTEM_STATE_OBJECT_ID": 0}}
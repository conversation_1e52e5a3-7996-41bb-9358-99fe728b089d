"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var SealService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SealService = void 0;
const common_1 = require("@nestjs/common");
const seal_1 = require("@mysten/seal");
const client_1 = require("@mysten/sui/client");
let SealService = SealService_1 = class SealService {
    logger = new common_1.Logger(SealService_1.name);
    sealClient = null;
    suiClient;
    isInitialized = false;
    async onModuleInit() {
        await this.initializeSeal();
    }
    async initializeSeal() {
        try {
            this.logger.log('Initializing Mysten SEAL library...');
            const suiRpcUrl = process.env.SUI_RPC_URL || 'https://fullnode.testnet.sui.io:443';
            this.suiClient = new client_1.SuiClient({ url: suiRpcUrl });
            const network = process.env.SUI_NETWORK || 'testnet';
            const keyServerIds = (0, seal_1.getAllowlistedKeyServers)(network);
            const serverConfigs = keyServerIds.map(objectId => ({
                objectId,
                weight: 1,
            }));
            this.sealClient = new seal_1.SealClient({
                suiClient: this.suiClient,
                serverConfigs,
                verifyKeyServers: false,
                timeout: 10000,
            });
            this.isInitialized = true;
            this.logger.log('✅ Mysten SEAL library initialized successfully');
            this.logger.log(`Using ${serverConfigs.length} key servers on ${network}`);
        }
        catch (error) {
            this.logger.error('Failed to initialize SEAL library:', error);
            throw new Error(`SEAL initialization failed: ${error.message}`);
        }
    }
    generateEncryptionId() {
        return `file_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    }
    async encryptFile(fileData, options) {
        try {
            if (!this.isInitialized || !this.sealClient) {
                throw new Error('SEAL client not initialized');
            }
            this.logger.log(`Starting Mysten SEAL encryption for file of size: ${fileData.length} bytes`);
            const data = fileData instanceof Buffer ? new Uint8Array(fileData) : fileData;
            const encryptionId = this.generateEncryptionId();
            const threshold = options.threshold || Math.ceil(3);
            const result = await this.sealClient.encrypt({
                packageId: options.packageId,
                id: encryptionId,
                data,
                threshold,
                aad: options.additionalData,
            });
            this.logger.log(`✅ File encrypted successfully with Mysten SEAL`);
            this.logger.log(`Encryption ID: ${encryptionId}`);
            return {
                success: true,
                encryptedData: result.encryptedObject,
                symmetricKey: result.key,
                encryptionId,
            };
        }
        catch (error) {
            this.logger.error('Failed to encrypt file:', error);
            return {
                success: false,
                error: error.message,
            };
        }
    }
    async decryptFile(encryptedData, sessionKey, txBytes) {
        try {
            if (!this.isInitialized || !this.sealClient) {
                throw new Error('SEAL client not initialized');
            }
            this.logger.log('Starting Mysten SEAL decryption...');
            const decryptedData = await this.sealClient.decrypt({
                data: encryptedData,
                sessionKey,
                txBytes,
            });
            this.logger.log(`✅ File decrypted successfully, size: ${decryptedData.length} bytes`);
            return {
                success: true,
                decryptedData,
            };
        }
        catch (error) {
            this.logger.error('Failed to decrypt file:', error);
            return {
                success: false,
                error: error.message,
            };
        }
    }
    async fetchKeys(encryptionIds, sessionKey, txBytes, threshold = 3) {
        try {
            if (!this.isInitialized || !this.sealClient) {
                throw new Error('SEAL client not initialized');
            }
            this.logger.log(`Fetching keys for ${encryptionIds.length} encryption IDs`);
            await this.sealClient.fetchKeys({
                ids: encryptionIds,
                sessionKey,
                txBytes,
                threshold,
            });
            this.logger.log('✅ Keys fetched successfully');
        }
        catch (error) {
            this.logger.error('Failed to fetch keys:', error);
            throw new Error(`Key fetching failed: ${error.message}`);
        }
    }
    async getKeyServers() {
        try {
            if (!this.isInitialized || !this.sealClient) {
                throw new Error('SEAL client not initialized');
            }
            return await this.sealClient.getKeyServers();
        }
        catch (error) {
            this.logger.error('Failed to get key servers:', error);
            throw new Error(`Failed to get key servers: ${error.message}`);
        }
    }
    isReady() {
        return this.isInitialized && this.sealClient !== null;
    }
    getSuiClient() {
        return this.suiClient;
    }
    getVersion() {
        return '@mysten/seal v0.4.18';
    }
};
exports.SealService = SealService;
exports.SealService = SealService = SealService_1 = __decorate([
    (0, common_1.Injectable)()
], SealService);
//# sourceMappingURL=seal.service.js.map
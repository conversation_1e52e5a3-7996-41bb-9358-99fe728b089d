# Voice Commands Setup Guide

This guide will help you set up voice commands for file attachment using Picovoice.

## Quick Start (Browser Speech Recognition)

The app currently uses the browser's built-in Speech Recognition API, which works immediately without any setup. You can:

1. Click the "Enable Voice" button on the landing page
2. Say commands like:
   - "attach file"
   - "upload file" 
   - "select file"
   - "browse files"

## Advanced Setup (Picovoice Integration)

For more accurate and customizable voice commands, you can integrate with Picovoice:

### Step 1: Get Picovoice Access Key

1. Go to [Picovoice Console](https://console.picovoice.ai/)
2. Sign up for a free account
3. Copy your access key

### Step 2: Configure Access Key

1. Open `frontend/src/config/picovoice.ts`
2. Replace `YOUR_PICOVOICE_ACCESS_KEY_HERE` with your actual access key

### Step 3: Create Custom Context (Optional)

For better file-related command recognition:

1. Go to [Picovoice Console](https://console.picovoice.ai/)
2. Navigate to Rhino section
3. Create a new context called "FileCommands"
4. Add these intents and expressions:

#### Intent: attachFile
- "attach file"
- "attach files" 
- "upload file"
- "upload files"
- "select file"
- "select files"

#### Intent: browseFiles
- "browse files"
- "open file browser"
- "choose file"
- "pick file"

#### Intent: startUpload
- "start upload"
- "begin upload"
- "upload now"

5. Download the `.rhn` context file
6. Place it in `frontend/public/` directory

### Step 4: Update Configuration

Update the Picovoice configuration to use your custom context:

```typescript
// In frontend/src/config/picovoice.ts
export const PICOVOICE_CONFIG = {
  ACCESS_KEY: 'your_actual_access_key_here',
  CONTEXT_PATH: '/FileCommands.rhn', // Path to your custom context
  // ... rest of config
};
```

## Supported Voice Commands

### File Operations
- **"attach file"** - Opens file browser
- **"upload file"** - Starts upload if file is selected
- **"select file"** - Opens file browser
- **"browse files"** - Opens file browser
- **"start upload"** - Begins upload process

### Navigation
- **"go back"** - Returns to previous screen
- **"show files"** - Navigate to file list

## Browser Compatibility

### Speech Recognition Support
- ✅ Chrome/Chromium browsers
- ✅ Edge
- ✅ Safari (limited)
- ❌ Firefox (limited support)

### Microphone Permissions
- The app requires microphone access
- You'll be prompted to allow microphone access
- For HTTPS sites, permissions are persistent
- For HTTP (localhost), you may need to grant permission each time

## Troubleshooting

### Voice Commands Not Working
1. Check if microphone permission is granted
2. Ensure you're using a supported browser
3. Check browser console for errors
4. Try refreshing the page

### Picovoice Integration Issues
1. Verify your access key is correct
2. Check that context files are in the correct location
3. Ensure you're using the latest Picovoice SDK version

### Audio Feedback Not Working
1. Check browser audio settings
2. Ensure speakers/headphones are connected
3. Check if other tabs are using audio

## Development Notes

### Adding New Voice Commands

1. Update the voice commands array in the component:
```typescript
const voiceCommands = [
  {
    command: 'your command',
    action: yourActionFunction,
    description: 'Description of what it does'
  }
];
```

2. The speech recognition will automatically match partial phrases

### Customizing Voice Feedback

Modify the `speakText` function in `useVoiceCommands.ts` to customize:
- Voice speed (rate)
- Voice pitch
- Voice volume
- Preferred voice selection

## Security Considerations

- Voice commands only work over HTTPS in production
- Microphone access requires user permission
- No voice data is sent to external servers (when using browser Speech Recognition)
- Picovoice processes audio locally on-device

## Performance Tips

- Voice recognition works best in quiet environments
- Speak clearly and at normal pace
- Wait for the "listening" indicator before speaking
- Use the exact command phrases for best recognition

## Future Enhancements

- [ ] Custom wake word detection
- [ ] Multi-language support
- [ ] Voice command shortcuts
- [ ] Voice-controlled navigation
- [ ] File management voice commands
- [ ] Integration with Alan AI for more natural conversations

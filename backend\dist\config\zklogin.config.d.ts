export interface ZkLoginConfig {
    oauth: {
        google: {
            clientId: string;
            redirectUri: string;
        };
        facebook: {
            clientId: string;
            redirectUri: string;
        };
        twitch: {
            clientId: string;
            redirectUri: string;
        };
        apple: {
            clientId: string;
            redirectUri: string;
        };
        github: {
            clientId: string;
            redirectUri: string;
        };
    };
    sui: {
        network: 'testnet' | 'mainnet' | 'devnet';
        rpcUrl: string;
        packageId?: string;
        registryId?: string;
    };
    zkLogin: {
        salt: string;
        maxEpoch: number;
        proverUrl: string;
    };
    jwt: {
        secret: string;
        expiresIn: string;
    };
}
export declare const defaultZkLoginConfig: ZkLoginConfig;
export declare enum OAuthProvider {
    GOOGLE = "google",
    FACEBOOK = "facebook",
    TWITCH = "twitch",
    APPLE = "apple",
    GITHUB = "github"
}
export declare const oauthProviderConfigs: {
    google: {
        authUrl: string;
        tokenUrl: string;
        userInfoUrl: string;
        scope: string;
    };
    facebook: {
        authUrl: string;
        tokenUrl: string;
        userInfoUrl: string;
        scope: string;
    };
    twitch: {
        authUrl: string;
        tokenUrl: string;
        userInfoUrl: string;
        scope: string;
    };
    apple: {
        authUrl: string;
        tokenUrl: string;
        userInfoUrl: string;
        scope: string;
    };
    github: {
        authUrl: string;
        tokenUrl: string;
        userInfoUrl: string;
        scope: string;
    };
};
export declare function validateZkLoginConfig(config: ZkLoginConfig): void;

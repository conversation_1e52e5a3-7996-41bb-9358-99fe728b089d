{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\bridge\\sources\\message_types.move", "definition_location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 93, "end": 106}, "module_name": ["000000000000000000000000000000000000000000000000000000000000000b", "message_types"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 323, "end": 355}, "definition_location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 334, "end": 339}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 343, "end": 345}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 348, "end": 353}}, "is_native": false}, "1": {"location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 359, "end": 419}, "definition_location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 370, "end": 389}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 393, "end": 395}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 398, "end": 417}}, "is_native": false}, "2": {"location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 423, "end": 469}, "definition_location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 434, "end": 446}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 450, "end": 452}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 455, "end": 467}}, "is_native": false}, "3": {"location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 473, "end": 533}, "definition_location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 484, "end": 503}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 507, "end": 509}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 512, "end": 531}}, "is_native": false}, "4": {"location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 537, "end": 595}, "definition_location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 548, "end": 566}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 570, "end": 572}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 575, "end": 593}}, "is_native": false}, "5": {"location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 599, "end": 655}, "definition_location": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 610, "end": 627}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 631, "end": 633}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 42, 114, 204, 211, 162, 52, 65, 76, 206, 102, 32, 3, 28, 81, 199, 248, 49, 180, 74, 129, 161, 196, 188, 29, 54, 173, 216, 164, 133, 134, 48], "start": 636, "end": 653}}, "is_native": false}}, "constant_map": {"ADD_TOKENS_ON_SUI": 5, "COMMITTEE_BLOCKLIST": 1, "EMERGENCY_OP": 2, "TOKEN": 0, "UPDATE_ASSET_PRICE": 4, "UPDATE_BRIDGE_LIMIT": 3}}
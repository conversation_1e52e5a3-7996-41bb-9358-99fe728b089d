"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SuiService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuiService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@mysten/sui/client");
const transactions_1 = require("@mysten/sui/transactions");
const zklogin_1 = require("@mysten/sui/zklogin");
const zklogin_config_1 = require("../config/zklogin.config");
let SuiService = SuiService_1 = class SuiService {
    logger = new common_1.Logger(SuiService_1.name);
    suiClient;
    config = zklogin_config_1.defaultZkLoginConfig;
    accessControlStorage = new Map();
    constructor() {
        this.suiClient = new client_1.SuiClient({ url: this.config.sui.rpcUrl });
    }
    async uploadFile(zkLoginAddress, cid, filename, fileSize) {
        try {
            if (!this.config.sui.packageId) {
                throw new Error('SuiCircle package ID not configured');
            }
            const tx = new transactions_1.Transaction();
            tx.moveCall({
                target: `${this.config.sui.packageId}::suicircle::upload_file`,
                arguments: [
                    tx.object(this.config.sui.registryId || '0x6'),
                    tx.pure.string(cid),
                    tx.pure.string(filename),
                    tx.pure.u64(fileSize),
                    tx.object('0x6'),
                ],
            });
            this.logger.log(`Would upload file ${filename} with CID ${cid} for user ${zkLoginAddress}`);
            return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        catch (error) {
            this.logger.error('Failed to upload file to smart contract', error);
            throw new Error('Failed to upload file to smart contract');
        }
    }
    async uploadFileWithZkLogin(cid, filename, fileSize, zkLoginParams) {
        try {
            if (!this.config.sui.packageId) {
                throw new Error('SuiCircle package ID not configured');
            }
            const tx = new transactions_1.Transaction();
            tx.moveCall({
                target: `${this.config.sui.packageId}::suicircle::upload_file`,
                arguments: [
                    tx.object('0x6'),
                    tx.pure.string(cid),
                    tx.pure.string(filename),
                    tx.pure.u64(fileSize),
                    tx.object('0x6'),
                ],
            });
            const result = await this.executeZkLoginTransaction(tx, zkLoginParams);
            this.logger.log(`Uploaded file ${filename} with CID ${cid}, transaction: ${result.digest}`);
            return result.digest;
        }
        catch (error) {
            this.logger.error('Failed to upload file with zkLogin', error);
            throw new Error('Failed to upload file with zkLogin');
        }
    }
    async grantFileAccess(zkLoginAddress, fileCid, authorizedAddress) {
        try {
            if (!this.config.sui.packageId) {
                throw new Error('SuiCircle package ID not configured');
            }
            const tx = new transactions_1.Transaction();
            tx.moveCall({
                target: `${this.config.sui.packageId}::suicircle::grant_access`,
                arguments: [
                    tx.object('0x6'),
                    tx.pure.string(fileCid),
                    tx.pure.address(authorizedAddress),
                    tx.object('0x6'),
                ],
            });
            this.logger.log(`Would grant access to file ${fileCid} for address ${authorizedAddress}`);
            return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        catch (error) {
            this.logger.error('Failed to grant file access', error);
            throw new Error('Failed to grant file access');
        }
    }
    async isAuthorizedForFile(fileCid, address) {
        try {
            if (!this.config.sui.packageId) {
                throw new Error('SuiCircle package ID not configured');
            }
            const result = await this.suiClient.devInspectTransactionBlock({
                transactionBlock: (() => {
                    const tx = new transactions_1.Transaction();
                    tx.moveCall({
                        target: `${this.config.sui.packageId}::suicircle::is_authorized`,
                        arguments: [
                            tx.object('0x6'),
                            tx.pure.string(fileCid),
                            tx.pure.address(address),
                        ],
                    });
                    return tx;
                })(),
                sender: address,
            });
            this.logger.log(`Checking authorization for ${address} on file ${fileCid}`);
            return true;
        }
        catch (error) {
            this.logger.error('Failed to check file authorization', error);
            return false;
        }
    }
    async getFileMetadata(fileCid) {
        try {
            if (!this.config.sui.packageId) {
                throw new Error('SuiCircle package ID not configured');
            }
            const result = await this.suiClient.devInspectTransactionBlock({
                transactionBlock: (() => {
                    const tx = new transactions_1.Transaction();
                    tx.moveCall({
                        target: `${this.config.sui.packageId}::suicircle::get_file_metadata`,
                        arguments: [
                            tx.object(this.config.sui.registryId || '0x6'),
                            tx.pure.string(fileCid),
                        ],
                    });
                    return tx;
                })(),
                sender: '0x0000000000000000000000000000000000000000000000000000000000000000',
            });
            this.logger.log(`Getting metadata for file ${fileCid}`);
            return {
                cid: fileCid,
                filename: 'example.txt',
                fileSize: 1024,
                uploadTimestamp: Date.now(),
                uploader: '0x1234567890abcdef',
                authorizedAddresses: ['0x1234567890abcdef'],
            };
        }
        catch (error) {
            this.logger.error('Failed to get file metadata', error);
            return null;
        }
    }
    async revokeFileAccess(zkLoginAddress, fileCid, addressToRemove) {
        try {
            if (!this.config.sui.packageId) {
                throw new Error('SuiCircle package ID not configured');
            }
            const tx = new transactions_1.Transaction();
            tx.moveCall({
                target: `${this.config.sui.packageId}::suicircle::revoke_access`,
                arguments: [
                    tx.object('0x6'),
                    tx.pure.string(fileCid),
                    tx.pure.address(addressToRemove),
                    tx.object('0x6'),
                ],
            });
            this.logger.log(`Would revoke access to file ${fileCid} for address ${addressToRemove}`);
            return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        catch (error) {
            this.logger.error('Failed to revoke file access', error);
            throw new Error('Failed to revoke file access');
        }
    }
    async executeZkLoginTransaction(tx, zkLoginParams) {
        try {
            const zkLoginAddress = this.deriveZkLoginAddress(zkLoginParams.jwt, zkLoginParams.userSalt);
            tx.setSender(zkLoginAddress);
            const txBytes = await tx.build({ client: this.suiClient });
            const ephemeralSignature = await zkLoginParams.ephemeralKeyPair.keypair.sign(txBytes);
            const zkLoginSignature = (0, zklogin_1.getZkLoginSignature)({
                inputs: {
                    ...zkLoginParams.zkLoginProof,
                    addressSeed: this.getAddressSeed(zkLoginParams.jwt, zkLoginParams.userSalt),
                },
                maxEpoch: zkLoginParams.ephemeralKeyPair.maxEpoch,
                userSignature: ephemeralSignature,
            });
            const result = await this.suiClient.executeTransactionBlock({
                transactionBlock: txBytes,
                signature: zkLoginSignature,
                options: {
                    showEffects: true,
                    showEvents: true,
                },
            });
            if (result.effects?.status?.status !== 'success') {
                throw new Error(`Transaction failed: ${result.effects?.status?.error}`);
            }
            return { digest: result.digest };
        }
        catch (error) {
            this.logger.error('Failed to execute zkLogin transaction', error);
            throw new Error('Failed to execute zkLogin transaction');
        }
    }
    deriveZkLoginAddress(jwt, salt) {
        const addressSeed = this.getAddressSeed(jwt, salt);
        return `0x${Buffer.from(addressSeed).toString('hex').slice(0, 40)}`;
    }
    getAddressSeed(jwt, salt) {
        const decoded = JSON.parse(Buffer.from(jwt.split('.')[1], 'base64').toString());
        return `${decoded.sub}_${decoded.iss}_${salt}`;
    }
    async grantFileAccessWithZkLogin(fileCid, authorizedAddress, zkLoginParams) {
        try {
            if (!this.config.sui.packageId) {
                throw new Error('SuiCircle package ID not configured');
            }
            const tx = new transactions_1.Transaction();
            tx.moveCall({
                target: `${this.config.sui.packageId}::suicircle::grant_access`,
                arguments: [
                    tx.object('0x6'),
                    tx.pure.string(fileCid),
                    tx.pure.address(authorizedAddress),
                    tx.object('0x6'),
                ],
            });
            const result = await this.executeZkLoginTransaction(tx, zkLoginParams);
            this.logger.log(`Granted access to file ${fileCid} for address ${authorizedAddress}`);
            return result.digest;
        }
        catch (error) {
            this.logger.error('Failed to grant file access with zkLogin', error);
            throw new Error('Failed to grant file access with zkLogin');
        }
    }
    async revokeFileAccessWithZkLogin(fileCid, addressToRemove, zkLoginParams) {
        try {
            if (!this.config.sui.packageId) {
                throw new Error('SuiCircle package ID not configured');
            }
            const tx = new transactions_1.Transaction();
            tx.moveCall({
                target: `${this.config.sui.packageId}::suicircle::revoke_access`,
                arguments: [
                    tx.object('0x6'),
                    tx.pure.string(fileCid),
                    tx.pure.address(addressToRemove),
                    tx.object('0x6'),
                ],
            });
            const result = await this.executeZkLoginTransaction(tx, zkLoginParams);
            this.logger.log(`Revoked access to file ${fileCid} for address ${addressToRemove}`);
            return result.digest;
        }
        catch (error) {
            this.logger.error('Failed to revoke file access with zkLogin', error);
            throw new Error('Failed to revoke file access with zkLogin');
        }
    }
    async createFileAccessControl(zkLoginAddress, fileCid, accessRule) {
        try {
            this.logger.log(`Creating access control for file ${fileCid} by ${zkLoginAddress}`);
            if (!this.config.sui.packageId) {
                throw new Error('SuiCircle package ID not configured');
            }
            const tx = new transactions_1.Transaction();
            const emailBytes = (accessRule.allowedEmails || []).map(email => Array.from(new TextEncoder().encode(email)));
            const allowedAddresses = accessRule.allowedAddresses || [];
            const suiNSBytes = (accessRule.allowedSuiNS || []).map(name => Array.from(new TextEncoder().encode(name)));
            tx.moveCall({
                target: `${this.config.sui.packageId}::suicircle::create_file_access_control`,
                arguments: [
                    tx.pure.vector('u8', Array.from(new TextEncoder().encode(fileCid))),
                    tx.pure.vector('u8', Array.from(new TextEncoder().encode(accessRule.conditionType))),
                    tx.pure.vector('vector<u8>', emailBytes),
                    tx.pure.vector('address', allowedAddresses),
                    tx.pure.vector('vector<u8>', suiNSBytes),
                    accessRule.accessStartTime ? tx.pure.option('u64', accessRule.accessStartTime) : tx.pure.option('u64', null),
                    accessRule.accessEndTime ? tx.pure.option('u64', accessRule.accessEndTime) : tx.pure.option('u64', null),
                    accessRule.maxAccessDuration ? tx.pure.option('u64', accessRule.maxAccessDuration) : tx.pure.option('u64', null),
                    tx.pure.bool(accessRule.requireAllConditions || false),
                    accessRule.maxAccessCount ? tx.pure.option('u64', accessRule.maxAccessCount) : tx.pure.option('u64', null),
                    tx.object('0x6'),
                ],
            });
            const accessControlInfo = {
                fileCid,
                owner: zkLoginAddress,
                conditionType: accessRule.conditionType,
                allowedEmails: accessRule.allowedEmails || [],
                allowedAddresses: accessRule.allowedAddresses || [],
                allowedSuiNS: accessRule.allowedSuiNS || [],
                accessStartTime: accessRule.accessStartTime,
                accessEndTime: accessRule.accessEndTime,
                requireAllConditions: accessRule.requireAllConditions || false,
                currentAccessCount: 0,
                totalUserRecords: 0,
            };
            this.accessControlStorage.set(fileCid, accessControlInfo);
            const simulatedDigest = `sui_tx_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
            this.logger.log(`Access control creation transaction prepared for file ${fileCid}: ${simulatedDigest}`);
            this.logger.log(`Transaction would be signed by: ${zkLoginAddress}`);
            this.logger.log(`Stored access control data:`, accessControlInfo);
            return simulatedDigest;
        }
        catch (error) {
            this.logger.error('Failed to create file access control', error);
            throw new Error(`Failed to create file access control: ${error.message}`);
        }
    }
    async updateFileAccessControl(zkLoginAddress, fileCid, accessRule) {
        try {
            this.logger.log(`Updating access control for file ${fileCid} by ${zkLoginAddress}`);
            if (!this.config.sui.packageId) {
                throw new Error('SuiCircle package ID not configured');
            }
            const tx = new transactions_1.Transaction();
            const emailBytes = (accessRule.allowedEmails || []).map(email => Array.from(new TextEncoder().encode(email)));
            const allowedAddresses = accessRule.allowedAddresses || [];
            const suiNSBytes = (accessRule.allowedSuiNS || []).map(name => Array.from(new TextEncoder().encode(name)));
            tx.moveCall({
                target: `${this.config.sui.packageId}::suicircle::update_file_access_control`,
                arguments: [
                    tx.pure.string(fileCid),
                    tx.pure.vector('u8', Array.from(new TextEncoder().encode(accessRule.conditionType))),
                    tx.pure.vector('vector<u8>', emailBytes),
                    tx.pure.vector('address', allowedAddresses),
                    tx.pure.vector('vector<u8>', suiNSBytes),
                    accessRule.accessStartTime ? tx.pure.option('u64', accessRule.accessStartTime) : tx.pure.option('u64', null),
                    accessRule.accessEndTime ? tx.pure.option('u64', accessRule.accessEndTime) : tx.pure.option('u64', null),
                    accessRule.maxAccessDuration ? tx.pure.option('u64', accessRule.maxAccessDuration) : tx.pure.option('u64', null),
                    tx.pure.bool(accessRule.requireAllConditions || false),
                    accessRule.maxAccessCount ? tx.pure.option('u64', accessRule.maxAccessCount) : tx.pure.option('u64', null),
                    tx.object('0x6'),
                ],
            });
            const accessControlInfo = {
                fileCid,
                owner: zkLoginAddress,
                conditionType: accessRule.conditionType,
                allowedEmails: accessRule.allowedEmails || [],
                allowedAddresses: accessRule.allowedAddresses || [],
                allowedSuiNS: accessRule.allowedSuiNS || [],
                accessStartTime: accessRule.accessStartTime,
                accessEndTime: accessRule.accessEndTime,
                requireAllConditions: accessRule.requireAllConditions || false,
                currentAccessCount: 0,
                totalUserRecords: 0,
            };
            this.accessControlStorage.set(fileCid, accessControlInfo);
            const simulatedDigest = `sui_tx_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
            this.logger.log(`Access control update transaction prepared for file ${fileCid}: ${simulatedDigest}`);
            this.logger.log(`Transaction would be signed by: ${zkLoginAddress}`);
            this.logger.log(`Updated access control data:`, accessControlInfo);
            return simulatedDigest;
        }
        catch (error) {
            this.logger.error('Failed to update file access control', error);
            throw new Error(`Failed to update file access control: ${error.message}`);
        }
    }
    async validateFileAccess(fileCid, userAddress, userEmail, userSuiNS) {
        try {
            this.logger.log(`Validating access for file ${fileCid} by ${userAddress}`);
            if (!this.config.sui.packageId) {
                this.logger.warn('SuiCircle package ID not configured, granting access for development');
                return true;
            }
            const accessControlInfo = await this.getFileAccessControlInfo(fileCid);
            if (!accessControlInfo) {
                this.logger.log(`No access control found for file ${fileCid}, granting access`);
                return true;
            }
            let addressValid = false;
            if (accessControlInfo.allowedAddresses.length === 0 ||
                accessControlInfo.allowedAddresses.includes(userAddress)) {
                addressValid = true;
            }
            let emailValid = false;
            if (accessControlInfo.allowedEmails.length === 0) {
                emailValid = true;
            }
            else if (userEmail && accessControlInfo.allowedEmails.includes(userEmail)) {
                emailValid = true;
            }
            let suiNSValid = false;
            if (!accessControlInfo.allowedSuiNS || accessControlInfo.allowedSuiNS.length === 0) {
                suiNSValid = true;
            }
            else if (userSuiNS && accessControlInfo.allowedSuiNS.includes(userSuiNS)) {
                suiNSValid = true;
            }
            let timeValid = true;
            const now = Date.now();
            if (accessControlInfo.accessStartTime && now < accessControlInfo.accessStartTime) {
                timeValid = false;
            }
            if (accessControlInfo.accessEndTime && now > accessControlInfo.accessEndTime) {
                timeValid = false;
            }
            const accessGranted = accessControlInfo.requireAllConditions
                ? (addressValid && emailValid && suiNSValid && timeValid)
                : (addressValid || emailValid || suiNSValid) && timeValid;
            this.logger.log(`Access validation result for file ${fileCid}: ${accessGranted}`);
            this.logger.log(`Address valid: ${addressValid}, Email valid: ${emailValid}, SuiNS valid: ${suiNSValid}, Time valid: ${timeValid}`);
            return accessGranted;
        }
        catch (error) {
            this.logger.error('Failed to validate file access', error);
            this.logger.warn('Granting access due to validation error (development mode)');
            return true;
        }
    }
    async getFileAccessControlInfo(fileCid) {
        try {
            this.logger.log(`Getting access control info for file ${fileCid}`);
            if (!this.config.sui.packageId) {
                throw new Error('SuiCircle package ID not configured');
            }
            try {
                const objects = await this.suiClient.getOwnedObjects({
                    owner: this.config.sui.registryId || '0x6',
                    filter: {
                        StructType: `${this.config.sui.packageId}::suicircle::FileAccessControl`
                    },
                    options: {
                        showContent: true,
                        showType: true,
                    }
                });
                for (const obj of objects.data) {
                    if (obj.data?.content && 'fields' in obj.data.content) {
                        const fields = obj.data.content.fields;
                        if (fields.file_cid === fileCid) {
                            const accessCondition = fields.access_condition;
                            return {
                                fileCid: fields.file_cid,
                                owner: fields.owner,
                                conditionType: accessCondition.condition_type,
                                allowedEmails: accessCondition.allowed_emails || [],
                                allowedAddresses: accessCondition.allowed_addresses || [],
                                allowedSuiNS: [],
                                accessStartTime: accessCondition.access_start_time,
                                accessEndTime: accessCondition.access_end_time,
                                requireAllConditions: accessCondition.require_all_conditions,
                                currentAccessCount: accessCondition.current_access_count,
                                totalUserRecords: fields.user_access_records?.length || 0,
                            };
                        }
                    }
                }
            }
            catch (queryError) {
                this.logger.warn(`Could not query smart contract for file ${fileCid}, returning mock data: ${queryError.message}`);
            }
            const storedInfo = this.accessControlStorage.get(fileCid);
            if (storedInfo) {
                this.logger.log(`Access control info retrieved for file ${fileCid} from storage`);
                return storedInfo;
            }
            this.logger.log(`No access control found for file ${fileCid}`);
            return null;
        }
        catch (error) {
            this.logger.error('Failed to get file access control info', error);
            return null;
        }
    }
};
exports.SuiService = SuiService;
exports.SuiService = SuiService = SuiService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], SuiService);
//# sourceMappingURL=sui.service.js.map
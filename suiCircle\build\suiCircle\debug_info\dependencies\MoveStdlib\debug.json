{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\move-stdlib\\sources\\debug.move", "definition_location": {"file_hash": [77, 191, 216, 236, 54, 79, 251, 169, 233, 79, 188, 148, 68, 203, 58, 125, 97, 207, 199, 195, 165, 151, 205, 138, 40, 35, 128, 54, 248, 17, 236, 55], "start": 133, "end": 138}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000001", "debug"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [77, 191, 216, 236, 54, 79, 251, 169, 233, 79, 188, 148, 68, 203, 58, 125, 97, 207, 199, 195, 165, 151, 205, 138, 40, 35, 128, 54, 248, 17, 236, 55], "start": 143, "end": 177}, "definition_location": {"file_hash": [77, 191, 216, 236, 54, 79, 251, 169, 233, 79, 188, 148, 68, 203, 58, 125, 97, 207, 199, 195, 165, 151, 205, 138, 40, 35, 128, 54, 248, 17, 236, 55], "start": 161, "end": 166}, "type_parameters": [["T", {"file_hash": [77, 191, 216, 236, 54, 79, 251, 169, 233, 79, 188, 148, 68, 203, 58, 125, 97, 207, 199, 195, 165, 151, 205, 138, 40, 35, 128, 54, 248, 17, 236, 55], "start": 167, "end": 168}]], "parameters": [["x#0#0", {"file_hash": [77, 191, 216, 236, 54, 79, 251, 169, 233, 79, 188, 148, 68, 203, 58, 125, 97, 207, 199, 195, 165, 151, 205, 138, 40, 35, 128, 54, 248, 17, 236, 55], "start": 170, "end": 171}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "1": {"location": {"file_hash": [77, 191, 216, 236, 54, 79, 251, 169, 233, 79, 188, 148, 68, 203, 58, 125, 97, 207, 199, 195, 165, 151, 205, 138, 40, 35, 128, 54, 248, 17, 236, 55], "start": 181, "end": 219}, "definition_location": {"file_hash": [77, 191, 216, 236, 54, 79, 251, 169, 233, 79, 188, 148, 68, 203, 58, 125, 97, 207, 199, 195, 165, 151, 205, 138, 40, 35, 128, 54, 248, 17, 236, 55], "start": 199, "end": 216}, "type_parameters": [], "parameters": [], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}
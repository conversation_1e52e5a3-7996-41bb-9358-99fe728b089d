"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const auth_service_1 = require("./auth.service");
const zklogin_config_1 = require("../config/zklogin.config");
let AuthController = AuthController_1 = class AuthController {
    authService;
    logger = new common_1.Logger(AuthController_1.name);
    constructor(authService) {
        this.authService = authService;
    }
    async initiateLogin(provider) {
        try {
            if (!Object.values(zklogin_config_1.OAuthProvider).includes(provider)) {
                throw new common_1.HttpException(`Unsupported OAuth provider: ${provider}`, common_1.HttpStatus.BAD_REQUEST);
            }
            const { sessionId, authUrl } = await this.authService.createSession(provider);
            return {
                success: true,
                data: {
                    sessionId,
                    authUrl,
                    provider,
                },
            };
        }
        catch (error) {
            this.logger.error('Failed to initiate login', error);
            throw new common_1.HttpException(error.message || 'Failed to initiate login', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async handleCallback(body) {
        try {
            const { sessionId, code, state } = body;
            this.logger.log(`OAuth callback received: sessionId=${sessionId}, code=${code?.substring(0, 10)}..., state=${state}`);
            if (!sessionId || !code) {
                this.logger.error('Missing required parameters in OAuth callback');
                throw new common_1.HttpException('Missing required parameters: sessionId and code', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log('Attempting to complete authentication...');
            const { token, user } = await this.authService.completeAuthentication(sessionId, code, state);
            this.logger.log('Authentication completed successfully');
            return {
                success: true,
                data: {
                    token,
                    user: {
                        zkLoginAddress: user.zkLoginAddress,
                        provider: user.provider,
                        email: user.email,
                        name: user.name,
                    },
                },
            };
        }
        catch (error) {
            this.logger.error('Failed to handle OAuth callback', error);
            throw new common_1.HttpException(error.message || 'Failed to complete authentication', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async verifyToken(authorization) {
        try {
            if (!authorization || !authorization.startsWith('Bearer ')) {
                throw new common_1.HttpException('Missing or invalid authorization header', common_1.HttpStatus.UNAUTHORIZED);
            }
            const token = authorization.substring(7);
            const user = await this.authService.verifyToken(token);
            if (!user) {
                throw new common_1.HttpException('Invalid or expired token', common_1.HttpStatus.UNAUTHORIZED);
            }
            return {
                success: true,
                data: {
                    user: {
                        zkLoginAddress: user.zkLoginAddress,
                        provider: user.provider,
                        email: user.email,
                        name: user.name,
                    },
                },
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error('Failed to verify token', error);
            throw new common_1.HttpException('Failed to verify token', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getProfile(authorization) {
        try {
            if (!authorization || !authorization.startsWith('Bearer ')) {
                throw new common_1.HttpException('Missing or invalid authorization header', common_1.HttpStatus.UNAUTHORIZED);
            }
            const token = authorization.substring(7);
            const user = await this.authService.verifyToken(token);
            if (!user) {
                throw new common_1.HttpException('Invalid or expired token', common_1.HttpStatus.UNAUTHORIZED);
            }
            return {
                success: true,
                data: {
                    zkLoginAddress: user.zkLoginAddress,
                    provider: user.provider,
                    email: user.email,
                    name: user.name,
                    sub: user.sub,
                    aud: user.aud,
                    iss: user.iss,
                },
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error('Failed to get profile', error);
            throw new common_1.HttpException('Failed to get profile', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async logout(authorization) {
        try {
            if (!authorization || !authorization.startsWith('Bearer ')) {
                throw new common_1.HttpException('Missing or invalid authorization header', common_1.HttpStatus.UNAUTHORIZED);
            }
            const token = authorization.substring(7);
            const user = await this.authService.verifyToken(token);
            if (user) {
                return {
                    success: true,
                    message: 'Logged out successfully',
                };
            }
            return {
                success: true,
                message: 'Already logged out',
            };
        }
        catch (error) {
            this.logger.error('Failed to logout', error);
            throw new common_1.HttpException('Failed to logout', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async checkFileAccess(fileCid, authorization) {
        try {
            if (!authorization || !authorization.startsWith('Bearer ')) {
                throw new common_1.HttpException('Missing or invalid authorization header', common_1.HttpStatus.UNAUTHORIZED);
            }
            const token = authorization.substring(7);
            const isAuthorized = await this.authService.isUserAuthorizedForFile(token, fileCid);
            return {
                success: true,
                data: {
                    fileCid,
                    authorized: isAuthorized,
                },
            };
        }
        catch (error) {
            this.logger.error('Failed to check file access', error);
            throw new common_1.HttpException('Failed to check file access', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Get)('login/:provider'),
    __param(0, (0, common_1.Param)('provider')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "initiateLogin", null);
__decorate([
    (0, common_1.Post)('callback'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "handleCallback", null);
__decorate([
    (0, common_1.Get)('verify'),
    __param(0, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "verifyToken", null);
__decorate([
    (0, common_1.Get)('profile'),
    __param(0, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Post)('logout'),
    __param(0, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "logout", null);
__decorate([
    (0, common_1.Get)('check-access/:fileCid'),
    __param(0, (0, common_1.Param)('fileCid')),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "checkFileAccess", null);
exports.AuthController = AuthController = AuthController_1 = __decorate([
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map
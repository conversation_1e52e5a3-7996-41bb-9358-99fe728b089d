{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\crypto\\groth16.move", "definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 90, "end": 97}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "groth16"], "struct_map": {"0": {"definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 978, "end": 983}, "type_parameters": [], "fields": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1013, "end": 1015}]}, "1": {"definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1436, "end": 1456}, "type_parameters": [], "fields": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1486, "end": 1507}, {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1526, "end": 1548}, {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1567, "end": 1588}, {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1607, "end": 1628}]}, "2": {"definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2478, "end": 2495}, "type_parameters": [], "fields": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2525, "end": 2530}]}, "3": {"definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3116, "end": 3127}, "type_parameters": [], "fields": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3157, "end": 3162}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1136, "end": 1184}, "definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1147, "end": 1155}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1159, "end": 1164}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1179, "end": 1180}, "1": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1167, "end": 1182}}, "is_native": false}, "1": {"location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1293, "end": 1338}, "definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1304, "end": 1309}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1313, "end": 1318}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1333, "end": 1334}, "1": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1321, "end": 1336}}, "is_native": false}, "2": {"location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1698, "end": 2079}, "definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1709, "end": 1723}, "type_parameters": [], "parameters": [["vk_gamma_abc_g1_bytes#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1730, "end": 1751}], ["alpha_g1_beta_g2_bytes#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1770, "end": 1792}], ["gamma_g2_neg_pc_bytes#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1811, "end": 1832}], ["delta_g2_neg_pc_bytes#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1851, "end": 1872}]], "returns": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1890, "end": 1910}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1950, "end": 1971}, "1": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1982, "end": 2004}, "2": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2015, "end": 2036}, "3": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2047, "end": 2068}, "4": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 1918, "end": 2076}}, "is_native": false}, "3": {"location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2156, "end": 2396}, "definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2167, "end": 2179}, "type_parameters": [], "parameters": [["pvk#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2180, "end": 2183}]], "returns": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2208, "end": 2226}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2251, "end": 2276}, "3": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2287, "end": 2313}, "6": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2324, "end": 2349}, "9": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2360, "end": 2385}, "12": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2234, "end": 2393}}, "is_native": false}, "4": {"location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2770, "end": 3019}, "definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2781, "end": 2811}, "type_parameters": [], "parameters": [["bytes#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2812, "end": 2817}]], "returns": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2832, "end": 2849}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2865, "end": 2870}, "1": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2865, "end": 2879}, "2": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2882, "end": 2884}, "3": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2880, "end": 2881}, "4": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2888, "end": 2889}, "5": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2885, "end": 2887}, "6": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2857, "end": 2906}, "8": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2891, "end": 2905}, "9": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2857, "end": 2906}, "10": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2921, "end": 2926}, "11": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2921, "end": 2935}, "12": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2938, "end": 2940}, "13": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2936, "end": 2937}, "14": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2944, "end": 2959}, "15": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2941, "end": 2943}, "16": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2913, "end": 2982}, "18": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2961, "end": 2981}, "19": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2913, "end": 2982}, "20": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3009, "end": 3014}, "21": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 2989, "end": 3016}}, "is_native": false}, "5": {"location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3231, "end": 3329}, "definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3242, "end": 3265}, "type_parameters": [], "parameters": [["bytes#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3266, "end": 3271}]], "returns": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3286, "end": 3297}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3319, "end": 3324}, "1": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3305, "end": 3326}}, "is_native": false}, "6": {"location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3797, "end": 3960}, "definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3808, "end": 3829}, "type_parameters": [], "parameters": [["curve#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3830, "end": 3835}], ["verifying_key#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3845, "end": 3858}]], "returns": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3874, "end": 3894}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3933, "end": 3938}, "1": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3933, "end": 3941}, "3": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3943, "end": 3956}, "4": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 3902, "end": 3957}}, "is_native": false}, "7": {"location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4123, "end": 4240}, "definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4134, "end": 4164}, "type_parameters": [], "parameters": [["curve#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4171, "end": 4176}], ["verifying_key#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4187, "end": 4200}]], "returns": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4219, "end": 4239}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "8": {"location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4668, "end": 5219}, "definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4679, "end": 4699}, "type_parameters": [], "parameters": [["curve#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4706, "end": 4711}], ["prepared_verifying_key#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4726, "end": 4748}], ["public_proof_inputs#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4778, "end": 4797}], ["proof_points#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4824, "end": 4836}]], "returns": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4856, "end": 4860}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4908, "end": 4913}, "1": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4908, "end": 4916}, "3": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4928, "end": 4950}, "4": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4927, "end": 4972}, "5": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4984, "end": 5006}, "6": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4983, "end": 5029}, "7": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5041, "end": 5063}, "8": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5040, "end": 5085}, "9": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5097, "end": 5119}, "10": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5096, "end": 5141}, "11": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5153, "end": 5172}, "12": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5152, "end": 5178}, "13": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5190, "end": 5202}, "14": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5189, "end": 5208}, "15": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 4868, "end": 5216}}, "is_native": false}, "9": {"location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5391, "end": 5694}, "definition_location": {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5402, "end": 5431}, "type_parameters": [], "parameters": [["curve#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5438, "end": 5443}], ["vk_gamma_abc_g1_bytes#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5454, "end": 5475}], ["alpha_g1_beta_g2_bytes#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5495, "end": 5517}], ["gamma_g2_neg_pc_bytes#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5537, "end": 5558}], ["delta_g2_neg_pc_bytes#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5578, "end": 5599}], ["public_proof_inputs#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5619, "end": 5638}], ["proof_points#0#0", {"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5658, "end": 5670}]], "returns": [{"file_hash": [227, 109, 51, 231, 89, 187, 176, 58, 93, 172, 237, 43, 249, 230, 171, 236, 234, 106, 220, 207, 68, 219, 241, 138, 165, 177, 122, 248, 74, 52, 38, 58], "start": 5689, "end": 5693}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EInvalidCurve": 1, "EInvalidScalar": 3, "EInvalidVerifyingKey": 0, "ETooManyPublicInputs": 2, "MaxPublicInputs": 4}}
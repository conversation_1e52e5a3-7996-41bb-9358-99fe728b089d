{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\move-stdlib\\sources\\address.move", "definition_location": {"file_hash": [174, 235, 52, 181, 250, 244, 35, 104, 204, 36, 75, 113, 108, 96, 154, 246, 150, 26, 129, 23, 142, 152, 163, 44, 33, 164, 65, 123, 40, 70, 183, 18], "start": 179, "end": 186}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000001", "address"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [174, 235, 52, 181, 250, 244, 35, 104, 204, 36, 75, 113, 108, 96, 154, 246, 150, 26, 129, 23, 142, 152, 163, 44, 33, 164, 65, 123, 40, 70, 183, 18], "start": 286, "end": 323}, "definition_location": {"file_hash": [174, 235, 52, 181, 250, 244, 35, 104, 204, 36, 75, 113, 108, 96, 154, 246, 150, 26, 129, 23, 142, 152, 163, 44, 33, 164, 65, 123, 40, 70, 183, 18], "start": 297, "end": 303}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [174, 235, 52, 181, 250, 244, 35, 104, 204, 36, 75, 113, 108, 96, 154, 246, 150, 26, 129, 23, 142, 152, 163, 44, 33, 164, 65, 123, 40, 70, 183, 18], "start": 307, "end": 310}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [174, 235, 52, 181, 250, 244, 35, 104, 204, 36, 75, 113, 108, 96, 154, 246, 150, 26, 129, 23, 142, 152, 163, 44, 33, 164, 65, 123, 40, 70, 183, 18], "start": 318, "end": 320}}, "is_native": false}}, "constant_map": {}}
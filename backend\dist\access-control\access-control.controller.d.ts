import { AccessControlService, CreateAccessControlRequest, UpdateAccessControlRequest, ValidateAccessRequest } from './access-control.service';
export declare class AccessControlController {
    private readonly accessControlService;
    private readonly logger;
    constructor(accessControlService: AccessControlService);
    createAccessControl(authorization: string, request: CreateAccessControlRequest, user: any): Promise<{
        success: boolean;
        data: {
            transactionDigest: string | undefined;
        };
        message: string;
    }>;
    updateAccessControl(authorization: string, request: UpdateAccessControlRequest, user: any): Promise<{
        success: boolean;
        data: {
            transactionDigest: string | undefined;
        };
        message: string;
    }>;
    validateAccess(authorization: string, request: ValidateAccessRequest, user: any): Promise<{
        success: boolean;
        data: {
            accessGranted: boolean | undefined;
        };
        message: string;
    }>;
    getAccessControlInfo(fileCid: string, authorization: string, user: any): Promise<{
        success: boolean;
        data: import("./access-control.service").AccessControlInfo | undefined;
        message: string;
    }>;
    checkAccess(fileCid: string, authorization: string, user: any): Promise<{
        success: boolean;
        data: {
            accessGranted: boolean | undefined;
            userAddress: string;
            userEmail: string | undefined;
        };
        message: string;
    }>;
    createAccessControlTest(request: CreateAccessControlRequest): Promise<{
        success: boolean;
        data: {
            transactionDigest: string;
        };
        message: string;
    }>;
    validateAccessTest(request: ValidateAccessRequest): Promise<{
        success: boolean;
        data: {
            accessGranted: boolean;
        };
        message: string;
    }>;
    getAccessControlInfoTest(fileCid: string): Promise<{
        success: boolean;
        data: {
            fileCid: string;
            owner: string;
            conditionType: string;
            allowedEmails: string[];
            allowedAddresses: string[];
            accessStartTime: number;
            accessEndTime: number;
            requireAllConditions: boolean;
            currentAccessCount: number;
            totalUserRecords: number;
        };
        message: string;
    }>;
    generateShareLink(authorization: string, request: {
        fileCid: string;
        expirationTime?: number;
        maxUses?: number;
    }, user: any): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
    generateShareLinkTest(request: {
        fileCid: string;
        expirationTime?: number;
        maxUses?: number;
    }): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
    accessViaShareLink(shareId: string, token?: string): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
    processBulkUpload(authorization: string, file: Express.Multer.File, body: {
        fileCid: string;
        conditionType: string;
    }, user: any): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
}

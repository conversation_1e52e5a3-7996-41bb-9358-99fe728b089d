# Sui Network Configuration
SUI_NETWORK=testnet
SUI_RPC_URL=https://fullnode.testnet.sui.io:443

# Walrus Configuration
WALRUS_NETWORK=testnet
WALRUS_PRIVATE_KEY=suiprivkey1q... # Your exported private key
WALRUS_STORAGE_EPOCHS=5 # How long to store files (in epochs)

# Upload Relay Configuration (Alternative to direct upload)
WALRUS_UPLOAD_RELAY_URL=https://upload-relay.testnet.walrus.space
WALRUS_USE_UPLOAD_RELAY=true # Set to true to use relay instead of direct upload
WALRUS_MAX_TIP=1000 # Maximum tip for upload relay (in MIST)

# Smart Contract Configuration (will be updated after deployment)
SUICIRCLE_PACKAGE_ID=0x****************************************aade340b2de23b8f87ce9120
SUICIRCLE_REGISTRY_ID=0x****************************************9f506226cd778164eeed994c

# Mysten SEAL Configuration
# Package ID for SEAL encryption (use your deployed package ID)
SUI_PACKAGE_ID=0x****************************************aade340b2de23b8f87ce9120
# The SEAL service automatically uses allowlisted key servers based on SUI_NETWORK


# zkLogin Configuration
ZKLOGIN_SALT=your-unique-salt-change-in-production
JWT_SECRET=your-jwt-secret-change-in-production

# OAuth Configuration (for zkLogin)
# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback

# Facebook OAuth
FACEBOOK_CLIENT_ID=your-facebook-app-id
FACEBOOK_CLIENT_SECRET=your-facebook-app-secret
FACEBOOK_REDIRECT_URI=http://localhost:3000/auth/facebook/callback

# Twitch OAuth
TWITCH_CLIENT_ID=your-twitch-client-id
TWITCH_CLIENT_SECRET=your-twitch-client-secret
TWITCH_REDIRECT_URI=http://localhost:3000/auth/twitch/callback

# Apple OAuth
APPLE_CLIENT_ID=your-apple-client-id
APPLE_CLIENT_SECRET=your-apple-client-secret
APPLE_REDIRECT_URI=http://localhost:3000/auth/apple/callback

# GitHub OAuth
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GITHUB_REDIRECT_URI=http://localhost:5174/

# Development
NODE_ENV=development
PORT=3000

{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-system\\sources\\validator_cap.move", "definition_location": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 97, "end": 110}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000003", "validator_cap"], "struct_map": {"0": {"definition_location": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 929, "end": 960}, "type_parameters": [], "fields": [{"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 983, "end": 985}, {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 997, "end": 1025}]}, "1": {"definition_location": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1198, "end": 1219}, "type_parameters": [], "fields": [{"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1236, "end": 1264}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1281, "end": 1435}, "definition_location": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1301, "end": 1333}, "type_parameters": [], "parameters": [["cap#0#0", {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1340, "end": 1343}]], "returns": [{"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1383, "end": 1391}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1400, "end": 1403}, "1": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1399, "end": 1432}}, "is_native": false}, "1": {"location": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1439, "end": 1572}, "definition_location": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1459, "end": 1489}, "type_parameters": [], "parameters": [["cap#0#0", {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1490, "end": 1493}]], "returns": [{"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1520, "end": 1528}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1537, "end": 1540}, "1": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1536, "end": 1569}}, "is_native": false}, "2": {"location": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1712, "end": 2485}, "definition_location": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1732, "end": 1783}, "type_parameters": [], "parameters": [["validator_address#0#0", {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1790, "end": 1807}], ["ctx#0#0", {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1823, "end": 1826}]], "returns": [{"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 1848, "end": 1850}], "locals": [["%#1", {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2114, "end": 2175}], ["operation_cap#1#0", {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2192, "end": 2205}], ["operation_cap_id#1#0", {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2348, "end": 2364}], ["sender_address#1#0", {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2070, "end": 2084}]], "nops": {}, "code_map": {"0": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2087, "end": 2090}, "2": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2087, "end": 2099}, "3": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2070, "end": 2084}, "4": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2114, "end": 2128}, "5": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2132, "end": 2136}, "6": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2129, "end": 2131}, "7": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2114, "end": 2175}, "11": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2140, "end": 2154}, "12": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2158, "end": 2175}, "13": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2155, "end": 2157}, "14": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2114, "end": 2175}, "16": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2106, "end": 2179}, "20": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2177, "end": 2178}, "21": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2106, "end": 2179}, "22": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2267, "end": 2270}, "23": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2255, "end": 2271}, "24": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2312, "end": 2329}, "25": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2208, "end": 2337}, "26": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2192, "end": 2205}, "27": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2378, "end": 2392}, "28": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2367, "end": 2393}, "29": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2348, "end": 2364}, "30": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2426, "end": 2439}, "31": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2441, "end": 2458}, "32": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2400, "end": 2459}, "33": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2466, "end": 2482}}, "is_native": false}, "3": {"location": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2640, "end": 2834}, "definition_location": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2660, "end": 2673}, "type_parameters": [], "parameters": [["cap#0#0", {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2674, "end": 2677}]], "returns": [{"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2714, "end": 2735}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2797, "end": 2800}, "1": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2797, "end": 2829}, "3": {"file_hash": [77, 204, 81, 233, 124, 85, 89, 207, 188, 17, 226, 10, 82, 34, 236, 141, 237, 65, 25, 21, 176, 149, 140, 204, 183, 50, 4, 106, 115, 128, 64, 61], "start": 2743, "end": 2831}}, "is_native": false}}, "constant_map": {}}
import { AuthService } from './auth.service';
import { OAuthProvider } from '../config/zklogin.config';
export declare class AuthController {
    private readonly authService;
    private readonly logger;
    constructor(authService: AuthService);
    initiateLogin(provider: string): Promise<{
        success: boolean;
        data: {
            sessionId: string;
            authUrl: string;
            provider: string;
        };
    }>;
    handleCallback(body: {
        sessionId: string;
        code: string;
        state?: string;
    }): Promise<{
        success: boolean;
        data: {
            token: string;
            user: {
                zkLoginAddress: string;
                provider: OAuthProvider;
                email: string | undefined;
                name: string | undefined;
            };
        };
    }>;
    verifyToken(authorization: string): Promise<{
        success: boolean;
        data: {
            user: {
                zkLoginAddress: string;
                provider: OAuthProvider;
                email: string | undefined;
                name: string | undefined;
            };
        };
    }>;
    getProfile(authorization: string): Promise<{
        success: boolean;
        data: {
            zkLoginAddress: string;
            provider: OAuthProvider;
            email: string | undefined;
            name: string | undefined;
            sub: string;
            aud: string;
            iss: string;
        };
    }>;
    logout(authorization: string): Promise<{
        success: boolean;
        message: string;
    }>;
    checkFileAccess(fileCid: string, authorization: string): Promise<{
        success: boolean;
        data: {
            fileCid: string;
            authorized: boolean;
        };
    }>;
}

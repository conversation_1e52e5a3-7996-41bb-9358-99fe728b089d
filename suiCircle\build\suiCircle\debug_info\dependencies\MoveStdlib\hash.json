{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\move-stdlib\\sources\\hash.move", "definition_location": {"file_hash": [229, 69, 181, 107, 7, 200, 106, 100, 4, 163, 34, 67, 20, 113, 31, 45, 210, 47, 223, 126, 84, 201, 21, 59, 152, 47, 131, 42, 28, 56, 163, 52], "start": 269, "end": 273}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000001", "hash"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [229, 69, 181, 107, 7, 200, 106, 100, 4, 163, 34, 67, 20, 113, 31, 45, 210, 47, 223, 126, 84, 201, 21, 59, 152, 47, 131, 42, 28, 56, 163, 52], "start": 278, "end": 335}, "definition_location": {"file_hash": [229, 69, 181, 107, 7, 200, 106, 100, 4, 163, 34, 67, 20, 113, 31, 45, 210, 47, 223, 126, 84, 201, 21, 59, 152, 47, 131, 42, 28, 56, 163, 52], "start": 296, "end": 304}, "type_parameters": [], "parameters": [["data#0#0", {"file_hash": [229, 69, 181, 107, 7, 200, 106, 100, 4, 163, 34, 67, 20, 113, 31, 45, 210, 47, 223, 126, 84, 201, 21, 59, 152, 47, 131, 42, 28, 56, 163, 52], "start": 305, "end": 309}]], "returns": [{"file_hash": [229, 69, 181, 107, 7, 200, 106, 100, 4, 163, 34, 67, 20, 113, 31, 45, 210, 47, 223, 126, 84, 201, 21, 59, 152, 47, 131, 42, 28, 56, 163, 52], "start": 324, "end": 334}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "1": {"location": {"file_hash": [229, 69, 181, 107, 7, 200, 106, 100, 4, 163, 34, 67, 20, 113, 31, 45, 210, 47, 223, 126, 84, 201, 21, 59, 152, 47, 131, 42, 28, 56, 163, 52], "start": 337, "end": 394}, "definition_location": {"file_hash": [229, 69, 181, 107, 7, 200, 106, 100, 4, 163, 34, 67, 20, 113, 31, 45, 210, 47, 223, 126, 84, 201, 21, 59, 152, 47, 131, 42, 28, 56, 163, 52], "start": 355, "end": 363}, "type_parameters": [], "parameters": [["data#0#0", {"file_hash": [229, 69, 181, 107, 7, 200, 106, 100, 4, 163, 34, 67, 20, 113, 31, 45, 210, 47, 223, 126, 84, 201, 21, 59, 152, 47, 131, 42, 28, 56, 163, 52], "start": 364, "end": 368}]], "returns": [{"file_hash": [229, 69, 181, 107, 7, 200, 106, 100, 4, 163, 34, 67, 20, 113, 31, 45, 210, 47, 223, 126, 84, 201, 21, 59, 152, 47, 131, 42, 28, 56, 163, 52], "start": 383, "end": 393}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}
{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\borrow.move", "definition_location": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 391, "end": 397}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "borrow"], "struct_map": {"0": {"definition_location": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 669, "end": 677}, "type_parameters": [["T", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 678, "end": 679}]], "fields": [{"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 711, "end": 713}, {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 729, "end": 734}]}, "1": {"definition_location": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 835, "end": 841}, "type_parameters": [], "fields": [{"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 844, "end": 847}, {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 858, "end": 861}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 907, "end": 1087}, "definition_location": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 918, "end": 921}, "type_parameters": [["T", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 922, "end": 923}]], "parameters": [["value#0#0", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 938, "end": 943}], ["ctx#0#0", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 948, "end": 951}]], "returns": [{"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 970, "end": 981}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1013, "end": 1016}, "1": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1013, "end": 1039}, "2": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1070, "end": 1075}, "3": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1057, "end": 1076}, "4": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 989, "end": 1084}}, "is_native": false}, "1": {"location": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1182, "end": 1441}, "definition_location": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1193, "end": 1199}, "type_parameters": [["T", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1200, "end": 1201}]], "parameters": [["self#0#0", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1216, "end": 1220}]], "returns": [{"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1242, "end": 1243}, {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1245, "end": 1251}], "locals": [["id#1#0", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1303, "end": 1305}], ["value#1#0", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1264, "end": 1269}]], "nops": {}, "code_map": {"0": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1272, "end": 1276}, "1": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1272, "end": 1282}, "2": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1272, "end": 1292}, "3": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1264, "end": 1269}, "4": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1319, "end": 1325}, "5": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1308, "end": 1326}, "6": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1303, "end": 1305}, "7": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1346, "end": 1351}, "8": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1389, "end": 1393}, "9": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1389, "end": 1396}, "11": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1416, "end": 1418}, "12": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1362, "end": 1430}, "13": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1335, "end": 1438}}, "is_native": false}, "2": {"location": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1498, "end": 1756}, "definition_location": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1509, "end": 1517}, "type_parameters": [["T", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1518, "end": 1519}]], "parameters": [["self#0#0", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1534, "end": 1538}], ["value#0#0", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1558, "end": 1563}], ["borrow#0#0", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1568, "end": 1574}]], "returns": [], "locals": [["obj#1#0", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1609, "end": 1612}], ["ref#1#0", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1604, "end": 1607}]], "nops": {}, "code_map": {"0": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1617, "end": 1623}, "1": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1595, "end": 1614}, "2": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1609, "end": 1612}, "3": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1604, "end": 1607}, "4": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1651, "end": 1657}, "5": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1640, "end": 1658}, "6": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1662, "end": 1665}, "7": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1659, "end": 1661}, "8": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1632, "end": 1679}, "12": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1667, "end": 1678}, "13": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1632, "end": 1679}, "14": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1694, "end": 1698}, "15": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1694, "end": 1701}, "17": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1705, "end": 1708}, "18": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1702, "end": 1704}, "19": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1686, "end": 1723}, "23": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1710, "end": 1722}, "24": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1686, "end": 1723}, "25": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1730, "end": 1734}, "26": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1730, "end": 1740}, "27": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1746, "end": 1751}, "28": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1730, "end": 1752}, "29": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1752, "end": 1753}}, "is_native": false}, "3": {"location": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1816, "end": 1946}, "definition_location": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1827, "end": 1834}, "type_parameters": [["T", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1835, "end": 1836}]], "parameters": [["self#0#0", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1851, "end": 1855}]], "returns": [{"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1871, "end": 1872}], "locals": [["value#1#0", {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1902, "end": 1907}]], "nops": {}, "code_map": {"0": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1912, "end": 1916}, "1": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1884, "end": 1909}, "2": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1902, "end": 1907}, "3": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1899, "end": 1900}, "4": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1923, "end": 1928}, "5": {"file_hash": [158, 235, 207, 144, 182, 113, 181, 82, 154, 176, 90, 211, 112, 196, 133, 34, 189, 102, 249, 164, 74, 248, 93, 134, 200, 109, 215, 135, 150, 103, 64, 65], "start": 1923, "end": 1943}}, "is_native": false}}, "constant_map": {"EWrongBorrow": 0, "EWrongValue": 1}}
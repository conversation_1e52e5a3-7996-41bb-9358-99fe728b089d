{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\bridge\\sources\\crypto.move", "definition_location": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 93, "end": 99}, "module_name": ["000000000000000000000000000000000000000000000000000000000000000b", "crypto"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 153, "end": 782}, "definition_location": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 173, "end": 201}, "type_parameters": [], "parameters": [["compressed_pub_key#0#0", {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 202, "end": 220}]], "returns": [{"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 236, "end": 246}], "locals": [["address#1#0", {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 639, "end": 646}], ["decompressed#1#0", {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 285, "end": 297}], ["decompressed_64#1#0", {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 400, "end": 415}], ["hash#1#0", {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 561, "end": 565}], ["i#1#0", {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 393, "end": 394}], ["i#2#0", {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 672, "end": 673}]], "nops": {}, "code_map": {"0": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 328, "end": 346}, "1": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 300, "end": 347}, "2": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 285, "end": 297}, "3": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 420, "end": 421}, "4": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 423, "end": 431}, "5": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 396, "end": 415}, "6": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 389, "end": 394}, "7": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 446, "end": 447}, "8": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 450, "end": 452}, "9": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 448, "end": 449}, "10": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 439, "end": 535}, "12": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 465, "end": 480}, "13": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 491, "end": 506}, "14": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 504, "end": 505}, "15": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 491, "end": 506}, "17": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 465, "end": 507}, "18": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 522, "end": 523}, "19": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 526, "end": 527}, "20": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 524, "end": 525}, "21": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 518, "end": 519}, "22": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 439, "end": 535}, "23": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 578, "end": 594}, "24": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 568, "end": 595}, "25": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 561, "end": 565}, "26": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 649, "end": 657}, "27": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 635, "end": 646}, "28": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 676, "end": 678}, "29": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 668, "end": 673}, "30": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 692, "end": 693}, "31": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 696, "end": 698}, "32": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 694, "end": 695}, "33": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 685, "end": 765}, "35": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 711, "end": 718}, "36": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 729, "end": 736}, "37": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 734, "end": 735}, "38": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 729, "end": 736}, "40": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 711, "end": 737}, "41": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 752, "end": 753}, "42": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 756, "end": 757}, "43": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 754, "end": 755}, "44": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 748, "end": 749}, "45": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 685, "end": 765}, "46": {"file_hash": [209, 62, 134, 131, 75, 172, 0, 180, 179, 169, 44, 84, 84, 233, 22, 231, 206, 85, 18, 3, 40, 79, 148, 8, 184, 150, 34, 251, 89, 22, 8, 33], "start": 772, "end": 779}}, "is_native": false}}, "constant_map": {}}
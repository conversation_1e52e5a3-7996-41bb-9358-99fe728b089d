{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\prover.move", "definition_location": {"file_hash": [75, 61, 80, 191, 162, 8, 138, 67, 42, 5, 225, 30, 231, 145, 124, 192, 241, 76, 40, 73, 23, 153, 105, 180, 45, 248, 61, 132, 237, 206, 138, 207], "start": 90, "end": 96}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "prover"], "struct_map": {}, "enum_map": {}, "function_map": {}, "constant_map": {}}
import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';
import { AuthService } from './auth.service';
export interface AuthenticatedRequest extends Request {
    user?: {
        zkLoginAddress: string;
        provider: string;
        email?: string;
        name?: string;
        sub: string;
        aud: string;
        iss: string;
    };
}
export declare class AuthGuard implements CanActivate {
    private readonly authService;
    private readonly logger;
    constructor(authService: AuthService);
    canActivate(context: ExecutionContext): Promise<boolean>;
    private extractTokenFromHeader;
}
export declare const CurrentUser: (...dataOrPipes: unknown[]) => ParameterDecorator;

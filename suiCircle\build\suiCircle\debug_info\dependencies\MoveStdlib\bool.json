{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\move-stdlib\\sources\\bool.move", "definition_location": {"file_hash": [135, 117, 68, 225, 175, 16, 253, 59, 179, 161, 119, 96, 134, 56, 224, 3, 67, 18, 223, 176, 210, 229, 244, 58, 33, 55, 192, 208, 201, 130, 61, 152], "start": 118, "end": 122}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000001", "bool"], "struct_map": {}, "enum_map": {}, "function_map": {}, "constant_map": {}}
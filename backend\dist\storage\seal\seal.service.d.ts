import { OnModuleInit } from '@nestjs/common';
import { SuiClient } from '@mysten/sui/client';
import { SessionKey } from '@mysten/seal';
export interface EncryptionResult {
    success: boolean;
    encryptedData?: Uint8Array;
    symmetricKey?: Uint8Array;
    encryptionId?: string;
    error?: string;
}
export interface DecryptionResult {
    success: boolean;
    decryptedData?: Uint8Array;
    error?: string;
}
export interface SealEncryptionOptions {
    packageId: string;
    identity: string;
    threshold?: number;
    additionalData?: Uint8Array;
}
export declare class SealService implements OnModuleInit {
    private readonly logger;
    private sealClient;
    private suiClient;
    private isInitialized;
    onModuleInit(): Promise<void>;
    private initializeSeal;
    private generateEncryptionId;
    encryptFile(fileData: Buffer | Uint8Array, options: SealEncryptionOptions): Promise<EncryptionResult>;
    decryptFile(encryptedData: Uint8Array, sessionKey: <PERSON>K<PERSON>, txBytes: Uint8Array): Promise<DecryptionResult>;
    fetchKeys(encryptionIds: string[], sessionKey: SessionKey, txBytes: Uint8Array, threshold?: number): Promise<void>;
    getKeyServers(): Promise<Map<string, import("@mysten/seal/dist/cjs/key-server").KeyServer>>;
    isReady(): boolean;
    getSuiClient(): SuiClient;
    getVersion(): string;
}

{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-system\\sources\\sui_system.move", "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 3448, "end": 3458}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000003", "sui_system"], "struct_map": {"0": {"definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4131, "end": 4145}, "type_parameters": [], "fields": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4161, "end": 4163}, {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4175, "end": 4182}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4445, "end": 5199}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4465, "end": 4471}, "type_parameters": [], "parameters": [["id#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4478, "end": 4480}], ["validators#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4492, "end": 4502}], ["storage_fund#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4528, "end": 4540}], ["protocol_version#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4561, "end": 4577}], ["epoch_start_timestamp_ms#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4589, "end": 4613}], ["parameters#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4625, "end": 4635}], ["stake_subsidy#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4660, "end": 4673}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4694, "end": 4697}]], "returns": [], "locals": [["self#1#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5037, "end": 5041}], ["system_state#1#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4729, "end": 4741}], ["version#1#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4958, "end": 4965}]], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4785, "end": 4795}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4806, "end": 4818}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4829, "end": 4845}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4856, "end": 4880}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4891, "end": 4901}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4912, "end": 4925}, "6": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4936, "end": 4939}, "7": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4744, "end": 4947}, "8": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4729, "end": 4741}, "9": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4968, "end": 5022}, "10": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 4958, "end": 4965}, "11": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5070, "end": 5072}, "12": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5083, "end": 5090}, "13": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5044, "end": 5098}, "14": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5033, "end": 5041}, "15": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5129, "end": 5136}, "16": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5124, "end": 5136}, "17": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5138, "end": 5145}, "18": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5147, "end": 5159}, "19": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5105, "end": 5160}, "20": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5190, "end": 5194}, "21": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5167, "end": 5195}, "22": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5195, "end": 5196}}, "is_native": false}, "1": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5807, "end": 6866}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5824, "end": 5855}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5862, "end": 5869}], ["pubkey_bytes#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5897, "end": 5909}], ["network_pubkey_bytes#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5928, "end": 5948}], ["worker_pubkey_bytes#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 5967, "end": 5986}], ["proof_of_possession#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6005, "end": 6024}], ["name#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6043, "end": 6047}], ["description#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6066, "end": 6077}], ["image_url#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6096, "end": 6105}], ["project_url#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6124, "end": 6135}], ["net_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6154, "end": 6165}], ["p2p_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6184, "end": 6195}], ["primary_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6214, "end": 6229}], ["worker_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6248, "end": 6262}], ["gas_price#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6281, "end": 6290}], ["commission_rate#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6302, "end": 6317}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6329, "end": 6332}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6360, "end": 6367}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6360, "end": 6401}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6458, "end": 6470}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6485, "end": 6505}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6520, "end": 6539}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6554, "end": 6573}, "6": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6588, "end": 6592}, "7": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6607, "end": 6618}, "8": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6633, "end": 6642}, "9": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6657, "end": 6668}, "10": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6683, "end": 6694}, "11": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6709, "end": 6720}, "12": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6735, "end": 6750}, "13": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6765, "end": 6779}, "14": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6794, "end": 6803}, "15": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6818, "end": 6833}, "16": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6848, "end": 6851}, "17": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 6360, "end": 6863}}, "is_native": false}, "2": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7008, "end": 7206}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7025, "end": 7059}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7066, "end": 7073}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7101, "end": 7104}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7132, "end": 7139}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7132, "end": 7163}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7199, "end": 7202}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7132, "end": 7203}}, "is_native": false}, "3": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7578, "end": 7736}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7595, "end": 7616}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7617, "end": 7624}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7647, "end": 7650}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7675, "end": 7682}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7675, "end": 7706}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7729, "end": 7732}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 7675, "end": 7733}}, "is_native": false}, "4": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8053, "end": 8217}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8070, "end": 8094}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8095, "end": 8102}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8125, "end": 8128}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8153, "end": 8160}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8153, "end": 8184}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8210, "end": 8213}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8153, "end": 8214}}, "is_native": false}, "5": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8381, "end": 8611}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8398, "end": 8419}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8426, "end": 8433}], ["cap#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8461, "end": 8464}], ["new_gas_price#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8505, "end": 8518}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8535, "end": 8542}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8535, "end": 8566}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8589, "end": 8592}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8594, "end": 8607}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8535, "end": 8608}}, "is_native": false}, "6": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8694, "end": 8948}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8711, "end": 8744}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8751, "end": 8758}], ["cap#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8786, "end": 8789}], ["new_gas_price#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8830, "end": 8843}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8860, "end": 8867}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8860, "end": 8891}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8926, "end": 8929}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8931, "end": 8944}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 8860, "end": 8945}}, "is_native": false}, "7": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9066, "end": 9302}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9083, "end": 9110}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9117, "end": 9124}], ["new_commission_rate#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9152, "end": 9171}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9183, "end": 9186}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9214, "end": 9221}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9214, "end": 9245}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9274, "end": 9293}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9295, "end": 9298}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9214, "end": 9299}}, "is_native": false}, "8": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9391, "end": 9671}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9408, "end": 9447}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9454, "end": 9461}], ["new_commission_rate#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9489, "end": 9508}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9520, "end": 9523}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9551, "end": 9558}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9551, "end": 9592}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9643, "end": 9662}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9664, "end": 9667}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9551, "end": 9668}}, "is_native": false}, "9": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9721, "end": 10030}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9738, "end": 9755}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9762, "end": 9769}], ["stake#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9797, "end": 9802}], ["validator_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9820, "end": 9837}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9853, "end": 9856}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9929, "end": 9936}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9938, "end": 9943}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9945, "end": 9962}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9964, "end": 9967}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9901, "end": 9968}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10013, "end": 10016}, "7": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10013, "end": 10025}, "8": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 9975, "end": 10026}, "9": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10026, "end": 10027}}, "is_native": false}, "10": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10156, "end": 10417}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10167, "end": 10194}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10201, "end": 10208}], ["stake#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10236, "end": 10241}], ["validator_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10259, "end": 10276}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10292, "end": 10295}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10317, "end": 10326}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10334, "end": 10341}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10334, "end": 10365}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10384, "end": 10389}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10391, "end": 10408}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10410, "end": 10413}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10334, "end": 10414}}, "is_native": false}, "11": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10488, "end": 10914}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10505, "end": 10531}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10538, "end": 10545}], ["stakes#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10573, "end": 10579}], ["stake_amount#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10605, "end": 10617}], ["validator_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10645, "end": 10662}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10678, "end": 10681}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10726, "end": 10733}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10726, "end": 10767}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10805, "end": 10811}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10813, "end": 10825}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10827, "end": 10844}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10846, "end": 10849}, "6": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10726, "end": 10850}, "7": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10897, "end": 10900}, "9": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10897, "end": 10909}, "10": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10859, "end": 10910}, "11": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10910, "end": 10911}}, "is_native": false}, "12": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10971, "end": 11272}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 10988, "end": 11010}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11017, "end": 11024}], ["staked_sui#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11052, "end": 11062}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11080, "end": 11083}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11133, "end": 11140}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11174, "end": 11184}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11186, "end": 11189}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11133, "end": 11190}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11249, "end": 11252}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11223, "end": 11253}, "6": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11255, "end": 11258}, "8": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11255, "end": 11267}, "9": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11197, "end": 11268}, "10": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11268, "end": 11269}}, "is_native": false}, "13": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11332, "end": 11575}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11343, "end": 11373}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11380, "end": 11387}], ["staked_sui#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11415, "end": 11425}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11443, "end": 11446}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11468, "end": 11485}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11493, "end": 11500}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11493, "end": 11524}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11556, "end": 11566}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11568, "end": 11571}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11493, "end": 11572}}, "is_native": false}, "14": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11635, "end": 11887}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11646, "end": 11672}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11679, "end": 11686}], ["fungible_staked_sui#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11714, "end": 11733}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11759, "end": 11762}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11780, "end": 11792}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11800, "end": 11807}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11800, "end": 11831}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11859, "end": 11878}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11880, "end": 11883}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 11800, "end": 11884}}, "is_native": false}, "15": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12015, "end": 12247}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12026, "end": 12058}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12065, "end": 12072}], ["staked_sui#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12100, "end": 12110}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12128, "end": 12131}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12153, "end": 12165}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12173, "end": 12180}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12173, "end": 12204}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12228, "end": 12238}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12240, "end": 12243}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12173, "end": 12244}}, "is_native": false}, "16": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12586, "end": 12810}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12603, "end": 12619}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12626, "end": 12633}], ["cap#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12661, "end": 12664}], ["reportee_addr#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12705, "end": 12718}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12739, "end": 12746}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12739, "end": 12770}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12788, "end": 12791}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12793, "end": 12806}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 12739, "end": 12807}}, "is_native": false}, "17": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13023, "end": 13257}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13040, "end": 13061}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13068, "end": 13075}], ["cap#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13103, "end": 13106}], ["reportee_addr#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13147, "end": 13160}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13181, "end": 13188}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13181, "end": 13212}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13235, "end": 13238}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13240, "end": 13253}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13181, "end": 13254}}, "is_native": false}, "18": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13459, "end": 13609}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13476, "end": 13496}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13497, "end": 13501}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13524, "end": 13527}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13552, "end": 13556}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13552, "end": 13580}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13602, "end": 13605}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13552, "end": 13606}}, "is_native": false}, "19": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13645, "end": 13836}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13662, "end": 13683}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13690, "end": 13694}], ["name#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13722, "end": 13726}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13745, "end": 13748}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13772, "end": 13776}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13772, "end": 13800}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13823, "end": 13827}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13829, "end": 13832}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13772, "end": 13833}}, "is_native": false}, "20": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13878, "end": 14097}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13895, "end": 13923}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13930, "end": 13934}], ["description#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13962, "end": 13973}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 13992, "end": 13995}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14019, "end": 14023}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14019, "end": 14047}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14077, "end": 14088}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14090, "end": 14093}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14019, "end": 14094}}, "is_native": false}, "21": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14137, "end": 14348}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14154, "end": 14180}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14187, "end": 14191}], ["image_url#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14219, "end": 14228}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14247, "end": 14250}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14274, "end": 14278}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14274, "end": 14302}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14330, "end": 14339}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14341, "end": 14344}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14274, "end": 14345}}, "is_native": false}, "22": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14390, "end": 14609}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14407, "end": 14435}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14442, "end": 14446}], ["project_url#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14474, "end": 14485}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14504, "end": 14507}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14531, "end": 14535}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14531, "end": 14559}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14589, "end": 14600}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14602, "end": 14605}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14531, "end": 14606}}, "is_native": false}, "23": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14725, "end": 14982}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14742, "end": 14785}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14792, "end": 14796}], ["network_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14824, "end": 14839}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14858, "end": 14861}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14885, "end": 14889}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14885, "end": 14913}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14958, "end": 14973}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14975, "end": 14978}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 14885, "end": 14979}}, "is_native": false}, "24": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15037, "end": 15292}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15054, "end": 15096}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15103, "end": 15107}], ["network_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15135, "end": 15150}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15169, "end": 15172}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15196, "end": 15200}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15196, "end": 15224}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15268, "end": 15283}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15285, "end": 15288}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15196, "end": 15289}}, "is_native": false}, "25": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15404, "end": 15645}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15421, "end": 15460}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15467, "end": 15471}], ["p2p_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15499, "end": 15510}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15529, "end": 15532}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15556, "end": 15560}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15556, "end": 15584}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15625, "end": 15636}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15638, "end": 15641}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15556, "end": 15642}}, "is_native": false}, "26": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15696, "end": 15935}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15713, "end": 15751}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15758, "end": 15762}], ["p2p_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15790, "end": 15801}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15820, "end": 15823}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15847, "end": 15851}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15847, "end": 15875}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15915, "end": 15926}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15928, "end": 15931}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 15847, "end": 15932}}, "is_native": false}, "27": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16059, "end": 16316}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16076, "end": 16119}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16126, "end": 16130}], ["primary_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16158, "end": 16173}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16192, "end": 16195}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16219, "end": 16223}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16219, "end": 16247}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16292, "end": 16307}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16309, "end": 16312}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16219, "end": 16313}}, "is_native": false}, "28": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16379, "end": 16634}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16396, "end": 16438}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16445, "end": 16449}], ["primary_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16477, "end": 16492}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16511, "end": 16514}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16538, "end": 16542}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16538, "end": 16566}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16610, "end": 16625}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16627, "end": 16630}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16538, "end": 16631}}, "is_native": false}, "29": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16757, "end": 17010}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16774, "end": 16816}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16823, "end": 16827}], ["worker_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16855, "end": 16869}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16888, "end": 16891}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16915, "end": 16919}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16915, "end": 16943}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16987, "end": 17001}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17003, "end": 17006}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 16915, "end": 17007}}, "is_native": false}, "30": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17072, "end": 17323}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17089, "end": 17130}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17137, "end": 17141}], ["worker_address#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17169, "end": 17183}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17202, "end": 17205}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17229, "end": 17233}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17229, "end": 17257}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17300, "end": 17314}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17316, "end": 17319}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17229, "end": 17320}}, "is_native": false}, "31": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17474, "end": 17810}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17491, "end": 17534}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17541, "end": 17545}], ["protocol_pubkey#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17573, "end": 17588}], ["proof_of_possession#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17607, "end": 17626}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17645, "end": 17648}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17672, "end": 17676}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17672, "end": 17710}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17765, "end": 17780}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17782, "end": 17801}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17803, "end": 17806}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17672, "end": 17807}}, "is_native": false}, "32": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17900, "end": 18234}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17917, "end": 17959}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17966, "end": 17970}], ["protocol_pubkey#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 17998, "end": 18013}], ["proof_of_possession#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18032, "end": 18051}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18070, "end": 18073}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18097, "end": 18101}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18097, "end": 18135}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18189, "end": 18204}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18206, "end": 18225}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18227, "end": 18230}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18097, "end": 18231}}, "is_native": false}, "33": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18359, "end": 18608}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18376, "end": 18417}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18424, "end": 18428}], ["worker_pubkey#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18456, "end": 18469}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18488, "end": 18491}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18515, "end": 18519}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18515, "end": 18543}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18586, "end": 18599}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18601, "end": 18604}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18515, "end": 18605}}, "is_native": false}, "34": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18672, "end": 18919}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18689, "end": 18729}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18736, "end": 18740}], ["worker_pubkey#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18768, "end": 18781}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18800, "end": 18803}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18827, "end": 18831}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18827, "end": 18855}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18897, "end": 18910}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18912, "end": 18915}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 18827, "end": 18916}}, "is_native": false}, "35": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19045, "end": 19298}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19062, "end": 19104}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19111, "end": 19115}], ["network_pubkey#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19143, "end": 19157}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19176, "end": 19179}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19203, "end": 19207}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19203, "end": 19231}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19275, "end": 19289}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19291, "end": 19294}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19203, "end": 19295}}, "is_native": false}, "36": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19363, "end": 19614}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19380, "end": 19421}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19428, "end": 19432}], ["network_pubkey#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19460, "end": 19474}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19493, "end": 19496}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19520, "end": 19524}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19520, "end": 19548}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19591, "end": 19605}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19607, "end": 19610}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19520, "end": 19611}}, "is_native": false}, "37": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19618, "end": 19790}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19629, "end": 19657}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19658, "end": 19665}], ["pool_id#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19688, "end": 19695}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19703, "end": 19710}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19718, "end": 19725}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19718, "end": 19749}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19779, "end": 19786}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19718, "end": 19787}}, "is_native": false}, "38": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19899, "end": 20094}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19910, "end": 19929}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19936, "end": 19943}], ["pool_id#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19971, "end": 19978}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 19989, "end": 20023}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20031, "end": 20038}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20031, "end": 20062}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20083, "end": 20090}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20031, "end": 20091}}, "is_native": false}, "39": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20166, "end": 20321}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20177, "end": 20203}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20204, "end": 20211}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20235, "end": 20250}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20258, "end": 20265}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20258, "end": 20289}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20258, "end": 20318}}, "is_native": false}, "40": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20435, "end": 20810}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20455, "end": 20472}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20479, "end": 20483}], ["staked_sui#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20511, "end": 20521}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20540, "end": 20543}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20561, "end": 20564}], "locals": [["%#1", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20700, "end": 20720}], ["%#2", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20628, "end": 20667}]], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20591, "end": 20595}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20591, "end": 20619}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20628, "end": 20667}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20700, "end": 20710}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20700, "end": 20720}, "7": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20628, "end": 20667}, "8": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20699, "end": 20720}, "9": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20628, "end": 20721}, "10": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20628, "end": 20754}, "11": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20783, "end": 20793}, "12": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20795, "end": 20798}, "13": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20795, "end": 20806}, "14": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 20628, "end": 20807}}, "is_native": false}, "41": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21242, "end": 22413}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21246, "end": 21259}, "type_parameters": [], "parameters": [["storage_reward#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21266, "end": 21280}], ["computation_reward#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21301, "end": 21319}], ["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21340, "end": 21347}], ["new_epoch#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21375, "end": 21384}], ["next_protocol_version#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21396, "end": 21417}], ["storage_rebate#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21429, "end": 21443}], ["non_refundable_storage_fee#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21455, "end": 21481}], ["storage_fund_reinvest_rate#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21493, "end": 21519}], ["reward_slashing_rate#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21627, "end": 21647}], ["epoch_start_timestamp_ms#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21722, "end": 21746}], ["ctx#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21790, "end": 21793}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21815, "end": 21827}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21917, "end": 21920}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21917, "end": 21929}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21933, "end": 21937}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21930, "end": 21932}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21909, "end": 21957}, "11": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21939, "end": 21956}, "12": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21909, "end": 21957}, "13": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21985, "end": 21992}, "14": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21985, "end": 22026}, "15": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22065, "end": 22074}, "16": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22089, "end": 22110}, "17": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22125, "end": 22139}, "18": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22154, "end": 22172}, "19": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22187, "end": 22201}, "20": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22216, "end": 22242}, "21": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22257, "end": 22283}, "22": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22298, "end": 22318}, "23": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22333, "end": 22357}, "24": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22372, "end": 22375}, "25": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 21985, "end": 22387}, "26": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22396, "end": 22410}}, "is_native": false}, "42": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22417, "end": 22530}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22421, "end": 22438}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22439, "end": 22443}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22467, "end": 22489}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22522, "end": 22526}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22497, "end": 22527}}, "is_native": false}, "43": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22534, "end": 22655}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22538, "end": 22559}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22560, "end": 22564}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22588, "end": 22614}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22647, "end": 22651}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22622, "end": 22652}}, "is_native": false}, "44": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22659, "end": 23215}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22663, "end": 22687}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22688, "end": 22692}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22716, "end": 22742}], "locals": [["inner#1#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23005, "end": 23010}], ["v2#1#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22878, "end": 22880}]], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22754, "end": 22758}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22754, "end": 22766}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22770, "end": 22771}, "4": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22767, "end": 22769}, "5": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22750, "end": 22992}, "6": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22841, "end": 22845}, "7": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22836, "end": 22848}, "8": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22850, "end": 22854}, "9": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22850, "end": 22862}, "11": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22814, "end": 22863}, "12": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22883, "end": 22896}, "13": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22878, "end": 22880}, "14": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22922, "end": 22923}, "15": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22907, "end": 22911}, "16": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22907, "end": 22919}, "17": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22907, "end": 22923}, "18": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22958, "end": 22962}, "19": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22953, "end": 22965}, "20": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22967, "end": 22971}, "21": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22967, "end": 22979}, "23": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22981, "end": 22983}, "24": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 22934, "end": 22984}, "25": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23082, "end": 23086}, "26": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23077, "end": 23089}, "27": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23100, "end": 23104}, "28": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23100, "end": 23112}, "30": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23041, "end": 23120}, "31": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23005, "end": 23010}, "32": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23135, "end": 23140}, "34": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23135, "end": 23163}, "35": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23167, "end": 23171}, "36": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23167, "end": 23179}, "38": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23164, "end": 23166}, "39": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23127, "end": 23200}, "43": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23181, "end": 23199}, "44": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23127, "end": 23200}, "45": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23207, "end": 23212}}, "is_native": false}, "45": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23349, "end": 23499}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23353, "end": 23376}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23377, "end": 23384}]], "returns": [{"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23408, "end": 23428}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23436, "end": 23443}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23436, "end": 23463}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23436, "end": 23496}}, "is_native": false}, "46": {"location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23662, "end": 23845}, "definition_location": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23666, "end": 23696}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23697, "end": 23704}], ["estimates_bytes#0#0", {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23727, "end": 23742}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23763, "end": 23770}, "1": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23763, "end": 23794}, "2": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23826, "end": 23841}, "3": {"file_hash": [121, 34, 88, 140, 234, 74, 40, 205, 107, 28, 23, 250, 128, 99, 40, 63, 60, 225, 198, 216, 91, 98, 158, 126, 145, 232, 51, 74, 176, 156, 33, 61], "start": 23763, "end": 23842}}, "is_native": false}}, "constant_map": {"ENotSystemAddress": 0, "EWrongInnerVersion": 1}}
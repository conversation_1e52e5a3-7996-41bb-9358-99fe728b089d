import { AppService } from './app.service';
import { FileService } from './file/file.service';
export declare class AppController {
    private readonly appService;
    private readonly fileService;
    private readonly logger;
    constructor(appService: AppService, fileService: FileService);
    getHello(): string;
    listUserFiles(authorization: string, user: any): Promise<{
        success: boolean;
        files: {
            cid: string;
            filename: string;
            fileSize: number;
            uploadTimestamp: number;
            uploader: string;
            isOwner: boolean;
        }[];
        data: {
            files: {
                cid: string;
                filename: string;
                fileSize: number;
                uploadTimestamp: number;
                uploader: string;
                isOwner: boolean;
            }[];
        };
        message: string;
    }>;
    listUserFilesTest(): Promise<{
        success: boolean;
        files: {
            cid: string;
            filename: string;
            fileSize: number;
            uploadTimestamp: number;
            uploader: string;
            isOwner: boolean;
        }[];
        data: {
            files: {
                cid: string;
                filename: string;
                fileSize: number;
                uploadTimestamp: number;
                uploader: string;
                isOwner: boolean;
            }[];
        };
        message: string;
    }>;
    clearUserFiles(authorization: string, user: any): Promise<{
        success: boolean;
        message: string;
    }>;
    clearUserFilesTest(): Promise<{
        success: boolean;
        message: string;
    }>;
}

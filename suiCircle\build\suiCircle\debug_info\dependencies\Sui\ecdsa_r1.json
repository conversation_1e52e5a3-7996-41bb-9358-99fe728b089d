{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\crypto\\ecdsa_r1.move", "definition_location": {"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 90, "end": 98}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "ecdsa_r1"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 1326, "end": 1447}, "definition_location": {"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 1344, "end": 1363}, "type_parameters": [], "parameters": [["signature#0#0", {"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 1370, "end": 1379}], ["msg#0#0", {"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 1399, "end": 1402}], ["hash#0#0", {"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 1422, "end": 1426}]], "returns": [{"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 1436, "end": 1446}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "1": {"location": {"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 2171, "end": 2313}, "definition_location": {"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 2189, "end": 2205}, "type_parameters": [], "parameters": [["signature#0#0", {"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 2212, "end": 2221}], ["public_key#0#0", {"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 2241, "end": 2251}], ["msg#0#0", {"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 2271, "end": 2274}], ["hash#0#0", {"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 2294, "end": 2298}]], "returns": [{"file_hash": [73, 84, 197, 72, 6, 32, 124, 60, 183, 186, 204, 15, 101, 128, 20, 167, 192, 191, 221, 57, 188, 189, 78, 53, 169, 36, 48, 231, 13, 140, 104, 31], "start": 2308, "end": 2312}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EFailToRecoverPubKey": 0, "EInvalidSignature": 1, "KECCAK256": 2, "SHA256": 3}}
{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\object_bag.move", "definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 441, "end": 451}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "object_bag"], "struct_map": {"0": {"definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 587, "end": 596}, "type_parameters": [], "fields": [{"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 647, "end": 649}, {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 711, "end": 715}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 758, "end": 882}, "definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 769, "end": 772}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 773, "end": 776}]], "returns": [{"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 795, "end": 804}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 849, "end": 852}, "1": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 837, "end": 853}, "2": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 870, "end": 871}, "3": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 812, "end": 879}}, "is_native": false}, "1": {"location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1064, "end": 1223}, "definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1075, "end": 1078}, "type_parameters": [["K", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1079, "end": 1080}], ["V", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1103, "end": 1104}]], "parameters": [["bag#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1119, "end": 1122}], ["k#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1140, "end": 1141}], ["v#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1146, "end": 1147}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1176, "end": 1179}, "1": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1171, "end": 1182}, "2": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1184, "end": 1185}, "3": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1187, "end": 1188}, "4": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1159, "end": 1189}, "5": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1207, "end": 1210}, "6": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1207, "end": 1215}, "8": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1218, "end": 1219}, "9": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1216, "end": 1217}, "10": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1196, "end": 1199}, "11": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1196, "end": 1204}, "12": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1196, "end": 1219}, "13": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1219, "end": 1220}}, "is_native": false}, "2": {"location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1599, "end": 1720}, "definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1610, "end": 1616}, "type_parameters": [["K", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1617, "end": 1618}], ["V", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1641, "end": 1642}]], "parameters": [["bag#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1657, "end": 1660}], ["k#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1674, "end": 1675}]], "returns": [{"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1681, "end": 1683}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1707, "end": 1710}, "1": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1706, "end": 1713}, "2": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1715, "end": 1716}, "3": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 1691, "end": 1717}}, "is_native": false}, "3": {"location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2098, "end": 2239}, "definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2109, "end": 2119}, "type_parameters": [["K", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2120, "end": 2121}], ["V", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2144, "end": 2145}]], "parameters": [["bag#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2160, "end": 2163}], ["k#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2181, "end": 2182}]], "returns": [{"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2188, "end": 2194}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2226, "end": 2229}, "1": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2221, "end": 2232}, "2": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2234, "end": 2235}, "3": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2202, "end": 2236}}, "is_native": false}, "4": {"location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2606, "end": 2780}, "definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2617, "end": 2623}, "type_parameters": [["K", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2624, "end": 2625}], ["V", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2648, "end": 2649}]], "parameters": [["bag#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2664, "end": 2667}], ["k#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2685, "end": 2686}]], "returns": [{"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2692, "end": 2693}], "locals": [["v#1#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2705, "end": 2706}]], "nops": {}, "code_map": {"0": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2729, "end": 2732}, "1": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2724, "end": 2735}, "2": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2737, "end": 2738}, "3": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2709, "end": 2739}, "4": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2705, "end": 2706}, "5": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2757, "end": 2760}, "6": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2757, "end": 2765}, "8": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2768, "end": 2769}, "9": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2766, "end": 2767}, "10": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2746, "end": 2749}, "11": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2746, "end": 2754}, "12": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2746, "end": 2769}, "13": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2776, "end": 2777}}, "is_native": false}, "5": {"location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2884, "end": 2997}, "definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2895, "end": 2903}, "type_parameters": [["K", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2904, "end": 2905}]], "parameters": [["bag#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2928, "end": 2931}], ["k#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2945, "end": 2946}]], "returns": [{"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2952, "end": 2956}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2984, "end": 2987}, "1": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2983, "end": 2990}, "2": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2992, "end": 2993}, "3": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 2964, "end": 2994}}, "is_native": false}, "6": {"location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3141, "end": 3292}, "definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3152, "end": 3170}, "type_parameters": [["K", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3171, "end": 3172}], ["V", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3195, "end": 3196}]], "parameters": [["bag#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3211, "end": 3214}], ["k#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3228, "end": 3229}]], "returns": [{"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3235, "end": 3239}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3279, "end": 3282}, "1": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3278, "end": 3285}, "2": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3287, "end": 3288}, "3": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3247, "end": 3289}}, "is_native": false}, "7": {"location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3360, "end": 3418}, "definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3371, "end": 3377}, "type_parameters": [], "parameters": [["bag#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3378, "end": 3381}]], "returns": [{"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3396, "end": 3399}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3407, "end": 3410}, "1": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3407, "end": 3415}}, "is_native": false}, "8": {"location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3487, "end": 3553}, "definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3498, "end": 3506}, "type_parameters": [], "parameters": [["bag#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3507, "end": 3510}]], "returns": [{"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3525, "end": 3529}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3537, "end": 3540}, "1": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3537, "end": 3545}, "3": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3549, "end": 3550}, "4": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3546, "end": 3548}, "5": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3537, "end": 3550}}, "is_native": false}, "9": {"location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3649, "end": 3789}, "definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3660, "end": 3673}, "type_parameters": [], "parameters": [["bag#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3674, "end": 3677}]], "returns": [], "locals": [["id#1#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3713, "end": 3715}], ["size#1#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3717, "end": 3721}]], "nops": {}, "code_map": {"0": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3726, "end": 3729}, "1": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3701, "end": 3723}, "2": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3717, "end": 3721}, "3": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3713, "end": 3715}, "4": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3744, "end": 3748}, "5": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3752, "end": 3753}, "6": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3749, "end": 3751}, "7": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3736, "end": 3768}, "9": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3755, "end": 3767}, "10": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3736, "end": 3768}, "11": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3775, "end": 3777}, "12": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3775, "end": 3786}}, "is_native": false}, "10": {"location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3919, "end": 4030}, "definition_location": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3930, "end": 3938}, "type_parameters": [["K", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3939, "end": 3940}]], "parameters": [["bag#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3963, "end": 3966}], ["k#0#0", {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3980, "end": 3981}]], "returns": [{"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 3987, "end": 3997}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 4017, "end": 4020}, "1": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 4016, "end": 4023}, "2": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 4025, "end": 4026}, "3": {"file_hash": [210, 51, 38, 126, 194, 246, 128, 142, 233, 172, 111, 1, 225, 206, 160, 250, 171, 86, 151, 6, 15, 238, 69, 77, 214, 159, 178, 4, 20, 209, 189, 227], "start": 4005, "end": 4027}}, "is_native": false}}, "constant_map": {"EBagNotEmpty": 0}}
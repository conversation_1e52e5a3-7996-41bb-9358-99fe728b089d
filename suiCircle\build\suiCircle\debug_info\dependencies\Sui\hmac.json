{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\crypto\\hmac.move", "definition_location": {"file_hash": [57, 185, 44, 45, 95, 134, 126, 85, 159, 249, 82, 153, 204, 42, 131, 248, 159, 16, 48, 213, 143, 131, 58, 180, 154, 110, 39, 156, 27, 175, 98, 210], "start": 90, "end": 94}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "hmac"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [57, 185, 44, 45, 95, 134, 126, 85, 159, 249, 82, 153, 204, 42, 131, 248, 159, 16, 48, 213, 143, 131, 58, 180, 154, 110, 39, 156, 27, 175, 98, 210], "start": 255, "end": 335}, "definition_location": {"file_hash": [57, 185, 44, 45, 95, 134, 126, 85, 159, 249, 82, 153, 204, 42, 131, 248, 159, 16, 48, 213, 143, 131, 58, 180, 154, 110, 39, 156, 27, 175, 98, 210], "start": 273, "end": 286}, "type_parameters": [], "parameters": [["key#0#0", {"file_hash": [57, 185, 44, 45, 95, 134, 126, 85, 159, 249, 82, 153, 204, 42, 131, 248, 159, 16, 48, 213, 143, 131, 58, 180, 154, 110, 39, 156, 27, 175, 98, 210], "start": 287, "end": 290}], ["msg#0#0", {"file_hash": [57, 185, 44, 45, 95, 134, 126, 85, 159, 249, 82, 153, 204, 42, 131, 248, 159, 16, 48, 213, 143, 131, 58, 180, 154, 110, 39, 156, 27, 175, 98, 210], "start": 305, "end": 308}]], "returns": [{"file_hash": [57, 185, 44, 45, 95, 134, 126, 85, 159, 249, 82, 153, 204, 42, 131, 248, 159, 16, 48, 213, 143, 131, 58, 180, 154, 110, 39, 156, 27, 175, 98, 210], "start": 324, "end": 334}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}
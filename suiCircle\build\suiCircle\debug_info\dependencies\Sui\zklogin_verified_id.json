{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\crypto\\zklogin_verified_id.move", "definition_location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 131, "end": 150}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "zklogin_verified_id"], "struct_map": {"0": {"definition_location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 349, "end": 359}, "type_parameters": [], "fields": [{"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 410, "end": 412}, {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 480, "end": 485}, {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 536, "end": 550}, {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 601, "end": 616}, {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 651, "end": 657}, {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 703, "end": 711}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 789, "end": 868}, "definition_location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 800, "end": 805}, "type_parameters": [], "parameters": [["verified_id#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 806, "end": 817}]], "returns": [{"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 833, "end": 840}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 848, "end": 859}, "1": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 848, "end": 865}}, "is_native": false}, "1": {"location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 948, "end": 1046}, "definition_location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 959, "end": 973}, "type_parameters": [], "parameters": [["verified_id#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 974, "end": 985}]], "returns": [{"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1001, "end": 1008}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1017, "end": 1028}, "1": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1016, "end": 1043}}, "is_native": false}, "2": {"location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1127, "end": 1227}, "definition_location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1138, "end": 1153}, "type_parameters": [], "parameters": [["verified_id#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1154, "end": 1165}]], "returns": [{"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1181, "end": 1188}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1197, "end": 1208}, "1": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1196, "end": 1224}}, "is_native": false}, "3": {"location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1292, "end": 1374}, "definition_location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1303, "end": 1309}, "type_parameters": [], "parameters": [["verified_id#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1310, "end": 1321}]], "returns": [{"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1337, "end": 1344}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1353, "end": 1364}, "1": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1352, "end": 1371}}, "is_native": false}, "4": {"location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1450, "end": 1536}, "definition_location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1461, "end": 1469}, "type_parameters": [], "parameters": [["verified_id#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1470, "end": 1481}]], "returns": [{"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1497, "end": 1504}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1513, "end": 1524}, "1": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1512, "end": 1533}}, "is_native": false}, "5": {"location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1565, "end": 1754}, "definition_location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1576, "end": 1582}, "type_parameters": [], "parameters": [["verified_id#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1583, "end": 1594}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1721, "end": 1732}, "1": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1619, "end": 1709}, "2": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1706, "end": 1707}, "3": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1693, "end": 1694}, "4": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1682, "end": 1683}, "5": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1662, "end": 1663}, "6": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1643, "end": 1644}, "7": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1739, "end": 1750}, "8": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1750, "end": 1751}}, "is_native": false}, "6": {"location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1796, "end": 2029}, "definition_location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1807, "end": 1824}, "type_parameters": [], "parameters": [["_key_claim_name#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1831, "end": 1846}], ["_key_claim_value#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1861, "end": 1877}], ["_issuer#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1892, "end": 1899}], ["_audience#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1914, "end": 1923}], ["_pin_hash#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1938, "end": 1947}], ["_ctx#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1960, "end": 1964}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2007, "end": 2024}, "1": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 1992, "end": 2025}}, "is_native": false}, "7": {"location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2071, "end": 2321}, "definition_location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2082, "end": 2098}, "type_parameters": [], "parameters": [["_address#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2105, "end": 2113}], ["_key_claim_name#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2129, "end": 2144}], ["_key_claim_value#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2160, "end": 2176}], ["_issuer#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2192, "end": 2199}], ["_audience#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2215, "end": 2224}], ["_pin_hash#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2240, "end": 2249}]], "returns": [{"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2261, "end": 2265}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2288, "end": 2305}, "1": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2273, "end": 2306}}, "is_native": false}, "8": {"location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2728, "end": 2942}, "definition_location": {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2739, "end": 2764}, "type_parameters": [], "parameters": [["address#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2771, "end": 2778}], ["key_claim_name#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2794, "end": 2808}], ["key_claim_value#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2828, "end": 2843}], ["issuer#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2863, "end": 2869}], ["audience#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2889, "end": 2897}], ["pin_hash#0#0", {"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2917, "end": 2925}]], "returns": [{"file_hash": [239, 136, 119, 148, 37, 94, 230, 165, 77, 25, 225, 127, 174, 95, 17, 182, 250, 197, 90, 34, 237, 58, 32, 20, 205, 85, 158, 246, 109, 166, 213, 18], "start": 2937, "end": 2941}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EFunctionDisabled": 0}}
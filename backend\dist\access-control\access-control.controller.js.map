{"version": 3, "file": "access-control.controller.js", "sourceRoot": "", "sources": ["../../src/access-control/access-control.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAewB;AACxB,+DAA2D;AAC3D,qEAKkC;AAClC,mDAA4D;AAGrD,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAGL;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnE,YAA6B,oBAA0C;QAA1C,yBAAoB,GAApB,oBAAoB,CAAsB;IAAG,CAAC;IAQrE,AAAN,KAAK,CAAC,mBAAmB,CACG,aAAqB,EACvC,OAAmC,EAC5B,IAAS;QAExB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAEnF,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,IAAI,sBAAa,CAAC,MAAM,CAAC,OAAO,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;YAClE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;iBAC5C;gBACD,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,iCAAiC,EAClD,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,mBAAmB,CACG,aAAqB,EACvC,OAAmC,EAC5B,IAAS;QAExB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAEnF,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,IAAI,sBAAa,CAAC,MAAM,CAAC,OAAO,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;YAClE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;iBAC5C;gBACD,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,iCAAiC,EAClD,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,cAAc,CACQ,aAAqB,EACvC,OAA8B,EACvB,IAAS;QAExB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAE9E,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,IAAI,EAAE;oBACJ,aAAa,EAAE,MAAM,CAAC,aAAa;iBACpC;gBACD,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,2BAA2B,EAC5C,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,oBAAoB,CACN,OAAe,EACP,aAAqB,EAChC,IAAS;QAExB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAEpF,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,IAAI,sBAAa,CAAC,MAAM,CAAC,OAAO,EAAE,mBAAU,CAAC,SAAS,CAAC,CAAC;YAChE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,sBAAa,EAAE,CAAC;gBACnC,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,IAAI,sBAAa,CACrB,mCAAmC,EACnC,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,WAAW,CACG,OAAe,EACP,aAAqB,EAChC,IAAS;QAExB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAGzC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACnF,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,sBAAa,CAAC,uBAAuB,EAAE,mBAAU,CAAC,YAAY,CAAC,CAAC;YAC5E,CAAC;YAED,MAAM,OAAO,GAA0B;gBACrC,OAAO;gBACP,WAAW,EAAE,QAAQ,CAAC,cAAc;gBACpC,SAAS,EAAE,QAAQ,CAAC,KAAK;aAC1B,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAE9E,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,IAAI,EAAE;oBACJ,aAAa,EAAE,MAAM,CAAC,aAAa;oBACnC,WAAW,EAAE,QAAQ,CAAC,cAAc;oBACpC,SAAS,EAAE,QAAQ,CAAC,KAAK;iBAC1B;gBACD,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,sBAAa,EAAE,CAAC;gBACnC,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,IAAI,sBAAa,CACrB,wBAAwB,EACxB,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,uBAAuB,CACnB,OAAmC;QAE3C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YAGvD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,iBAAiB,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;iBACtF;gBACD,OAAO,EAAE,iDAAiD;aAC3D,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,iCAAiC,EAClD,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,kBAAkB,CACd,OAA8B;QAEtC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;YAGjD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,aAAa,EAAE,IAAI;iBACpB;gBACD,OAAO,EAAE,4BAA4B;aACtC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,2BAA2B,EAC5C,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,wBAAwB,CACV,OAAe;QAEjC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qDAAqD,OAAO,EAAE,CAAC,CAAC;YAGhF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,OAAO;oBACP,KAAK,EAAE,YAAY;oBACnB,aAAa,EAAE,QAAQ;oBACvB,aAAa,EAAE,CAAC,kBAAkB,CAAC;oBACnC,gBAAgB,EAAE,CAAC,oBAAoB,CAAC;oBACxC,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE;oBAC3B,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ;oBACpC,oBAAoB,EAAE,KAAK;oBAC3B,kBAAkB,EAAE,CAAC;oBACrB,gBAAgB,EAAE,CAAC;iBACpB;gBACD,OAAO,EAAE,+DAA+D;aACzE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,IAAI,sBAAa,CACrB,mCAAmC,EACnC,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,iBAAiB,CACK,aAAqB,EACvC,OAAuE,EAChE,IAAS;QAExB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAEjF,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,IAAI,sBAAa,CAAC,MAAM,CAAC,OAAO,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;YAClE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,+BAA+B,EAChD,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,qBAAqB,CACjB,OAAuE;QAE/E,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAE9E,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,+BAA+B,EAChD,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,kBAAkB,CACJ,OAAe,EACjB,KAAc;QAE9B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAEjF,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,IAAI,sBAAa,CAAC,MAAM,CAAC,OAAO,EAAE,mBAAU,CAAC,SAAS,CAAC,CAAC;YAChE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,uBAAuB,EACxC,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,iBAAiB,CACK,aAAqB,EAC/B,IAAyB,EACjC,IAAgD,EACzC,IAAS;QAExB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,sBAAa,CAAC,kBAAkB,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;YACtE,CAAC;YAED,MAAM,KAAK,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAC9D,KAAK,EACL,IAAI,EACJ,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,aAA8C,CACpD,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,IAAI,sBAAa,CAAC,MAAM,CAAC,OAAO,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;YAClE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,+BAA+B,EAChD,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAnaY,0DAAuB;AAW5B;IAFL,IAAA,aAAI,GAAE;IACN,IAAA,kBAAS,EAAC,sBAAS,CAAC;IAElB,WAAA,IAAA,gBAAO,EAAC,eAAe,CAAC,CAAA;IACxB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,wBAAW,GAAE,CAAA;;;;kEAwBf;AAQK;IAFL,IAAA,YAAG,GAAE;IACL,IAAA,kBAAS,EAAC,sBAAS,CAAC;IAElB,WAAA,IAAA,gBAAO,EAAC,eAAe,CAAC,CAAA;IACxB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,wBAAW,GAAE,CAAA;;;;kEAwBf;AAQK;IAFL,IAAA,aAAI,EAAC,UAAU,CAAC;IAChB,IAAA,kBAAS,EAAC,sBAAS,CAAC;IAElB,WAAA,IAAA,gBAAO,EAAC,eAAe,CAAC,CAAA;IACxB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,wBAAW,GAAE,CAAA;;;;6DAoBf;AAQK;IAFL,IAAA,YAAG,EAAC,UAAU,CAAC;IACf,IAAA,kBAAS,EAAC,sBAAS,CAAC;IAElB,WAAA,IAAA,cAAK,EAAC,SAAS,CAAC,CAAA;IAChB,WAAA,IAAA,gBAAO,EAAC,eAAe,CAAC,CAAA;IACxB,WAAA,IAAA,wBAAW,GAAE,CAAA;;;;mEA0Bf;AAQK;IAFL,IAAA,YAAG,EAAC,gBAAgB,CAAC;IACrB,IAAA,kBAAS,EAAC,sBAAS,CAAC;IAElB,WAAA,IAAA,cAAK,EAAC,SAAS,CAAC,CAAA;IAChB,WAAA,IAAA,gBAAO,EAAC,eAAe,CAAC,CAAA;IACxB,WAAA,IAAA,wBAAW,GAAE,CAAA;;;;0DAuCf;AASK;IADL,IAAA,aAAI,EAAC,MAAM,CAAC;IAEV,WAAA,IAAA,aAAI,GAAE,CAAA;;;;sEAoBR;AAOK;IADL,IAAA,aAAI,EAAC,eAAe,CAAC;IAEnB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;iEAoBR;AAOK;IADL,IAAA,YAAG,EAAC,eAAe,CAAC;IAElB,WAAA,IAAA,cAAK,EAAC,SAAS,CAAC,CAAA;;;;uEA6BlB;AAQK;IAFL,IAAA,aAAI,EAAC,YAAY,CAAC;IAClB,IAAA,kBAAS,EAAC,sBAAS,CAAC;IAElB,WAAA,IAAA,gBAAO,EAAC,eAAe,CAAC,CAAA;IACxB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,wBAAW,GAAE,CAAA;;;;gEAsBf;AAOK;IADL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IAErB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;oEAiBR;AAOK;IADL,IAAA,YAAG,EAAC,gBAAgB,CAAC;IAEnB,WAAA,IAAA,cAAK,EAAC,SAAS,CAAC,CAAA;IAChB,WAAA,IAAA,cAAK,EAAC,OAAO,CAAC,CAAA;;;;iEAqBhB;AASK;IAHL,IAAA,aAAI,EAAC,aAAa,CAAC;IACnB,IAAA,kBAAS,EAAC,sBAAS,CAAC;IACpB,IAAA,wBAAe,EAAC,IAAA,kCAAe,EAAC,MAAM,CAAC,CAAC;IAEtC,WAAA,IAAA,gBAAO,EAAC,eAAe,CAAC,CAAA;IACxB,WAAA,IAAA,qBAAY,GAAE,CAAA;IACd,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,wBAAW,GAAE,CAAA;;;;gEA+Bf;kCAlaU,uBAAuB;IADnC,IAAA,mBAAU,EAAC,gBAAgB,CAAC;qCAIwB,6CAAoB;GAH5D,uBAAuB,CAmanC"}
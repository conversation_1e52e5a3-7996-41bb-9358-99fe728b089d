{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\move-stdlib\\sources\\bcs.move", "definition_location": {"file_hash": [25, 30, 173, 81, 43, 162, 183, 75, 174, 137, 10, 226, 12, 205, 244, 58, 90, 76, 238, 135, 162, 182, 76, 17, 28, 202, 102, 148, 171, 148, 192, 93], "start": 402, "end": 405}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000001", "bcs"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [25, 30, 173, 81, 43, 162, 183, 75, 174, 137, 10, 226, 12, 205, 244, 58, 90, 76, 238, 135, 162, 182, 76, 17, 28, 202, 102, 148, 171, 148, 192, 93], "start": 502, "end": 567}, "definition_location": {"file_hash": [25, 30, 173, 81, 43, 162, 183, 75, 174, 137, 10, 226, 12, 205, 244, 58, 90, 76, 238, 135, 162, 182, 76, 17, 28, 202, 102, 148, 171, 148, 192, 93], "start": 520, "end": 528}, "type_parameters": [["MoveValue", {"file_hash": [25, 30, 173, 81, 43, 162, 183, 75, 174, 137, 10, 226, 12, 205, 244, 58, 90, 76, 238, 135, 162, 182, 76, 17, 28, 202, 102, 148, 171, 148, 192, 93], "start": 529, "end": 538}]], "parameters": [["v#0#0", {"file_hash": [25, 30, 173, 81, 43, 162, 183, 75, 174, 137, 10, 226, 12, 205, 244, 58, 90, 76, 238, 135, 162, 182, 76, 17, 28, 202, 102, 148, 171, 148, 192, 93], "start": 540, "end": 541}]], "returns": [{"file_hash": [25, 30, 173, 81, 43, 162, 183, 75, 174, 137, 10, 226, 12, 205, 244, 58, 90, 76, 238, 135, 162, 182, 76, 17, 28, 202, 102, 148, 171, 148, 192, 93], "start": 556, "end": 566}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}
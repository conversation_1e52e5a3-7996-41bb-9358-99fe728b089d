{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\crypto\\zklogin_verified_issuer.move", "definition_location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 114, "end": 137}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "zklogin_verified_issuer"], "struct_map": {"0": {"definition_location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 593, "end": 607}, "type_parameters": [], "fields": [{"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 662, "end": 664}, {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 732, "end": 737}, {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 773, "end": 779}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 861, "end": 952}, "definition_location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 872, "end": 877}, "type_parameters": [], "parameters": [["verified_issuer#0#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 878, "end": 893}]], "returns": [{"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 913, "end": 920}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 928, "end": 943}, "1": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 928, "end": 949}}, "is_native": false}, "1": {"location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1021, "end": 1115}, "definition_location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1032, "end": 1038}, "type_parameters": [], "parameters": [["verified_issuer#0#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1039, "end": 1054}]], "returns": [{"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1074, "end": 1081}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1090, "end": 1105}, "1": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1089, "end": 1112}}, "is_native": false}, "2": {"location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1148, "end": 1292}, "definition_location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1159, "end": 1165}, "type_parameters": [], "parameters": [["verified_issuer#0#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1166, "end": 1181}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1255, "end": 1270}, "1": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1210, "end": 1252}, "2": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1249, "end": 1250}, "3": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1238, "end": 1239}, "4": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1277, "end": 1288}, "5": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1288, "end": 1289}}, "is_native": false}, "3": {"location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1531, "end": 1910}, "definition_location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1542, "end": 1563}, "type_parameters": [], "parameters": [["address_seed#0#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1564, "end": 1576}], ["issuer#0#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1584, "end": 1590}], ["ctx#0#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1600, "end": 1603}]], "returns": [], "locals": [["sender#1#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1632, "end": 1638}]], "nops": {}, "code_map": {"0": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1641, "end": 1644}, "2": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1641, "end": 1653}, "3": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1632, "end": 1638}, "4": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1689, "end": 1695}, "5": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1697, "end": 1709}, "6": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1711, "end": 1718}, "7": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1668, "end": 1719}, "8": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1660, "end": 1735}, "12": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1721, "end": 1734}, "13": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1660, "end": 1735}, "14": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1817, "end": 1820}, "15": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1805, "end": 1821}, "16": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1843, "end": 1849}, "17": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1864, "end": 1870}, "18": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1771, "end": 1882}, "19": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1893, "end": 1899}, "20": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 1742, "end": 1907}}, "is_native": false}, "4": {"location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2011, "end": 2185}, "definition_location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2022, "end": 2042}, "type_parameters": [], "parameters": [["address#0#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2043, "end": 2050}], ["address_seed#0#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2061, "end": 2073}], ["issuer#0#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2081, "end": 2087}]], "returns": [{"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2099, "end": 2103}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2141, "end": 2148}, "1": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2150, "end": 2162}, "2": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2164, "end": 2170}, "3": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2164, "end": 2181}, "4": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2111, "end": 2182}}, "is_native": false}, "5": {"location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2372, "end": 2497}, "definition_location": {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2383, "end": 2412}, "type_parameters": [], "parameters": [["address#0#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2419, "end": 2426}], ["address_seed#0#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2442, "end": 2454}], ["issuer#0#0", {"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2467, "end": 2473}]], "returns": [{"file_hash": [40, 77, 55, 28, 99, 254, 163, 140, 67, 53, 68, 174, 77, 130, 12, 223, 76, 240, 121, 230, 221, 54, 200, 47, 159, 19, 174, 4, 22, 58, 111, 182], "start": 2492, "end": 2496}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EInvalidInput": 0, "EInvalidProof": 1}}
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AccessControlService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccessControlService = void 0;
const common_1 = require("@nestjs/common");
const sui_service_1 = require("../sui/sui.service");
const auth_service_1 = require("../auth/auth.service");
const utils_1 = require("@mysten/sui/utils");
let AccessControlService = AccessControlService_1 = class AccessControlService {
    suiService;
    authService;
    logger = new common_1.Logger(AccessControlService_1.name);
    shareLinks = new Map();
    constructor(suiService, authService) {
        this.suiService = suiService;
        this.authService = authService;
    }
    async createAccessControl(token, request) {
        try {
            const user = await this.authService.verifyToken(token);
            if (!user) {
                return {
                    success: false,
                    message: 'Authentication failed',
                };
            }
            this.logger.log(`Creating access control for file ${request.fileCid} by ${user.zkLoginAddress}`);
            const validationResult = this.validateAccessRule(request.accessRule);
            if (!validationResult.valid) {
                return {
                    success: false,
                    message: `Invalid access rule: ${validationResult.error}`,
                };
            }
            const transactionDigest = await this.suiService.createFileAccessControl(user.zkLoginAddress, request.fileCid, request.accessRule);
            this.logger.log(`Access control created for file ${request.fileCid}: ${transactionDigest}`);
            return {
                success: true,
                message: 'Access control created successfully',
                transactionDigest,
            };
        }
        catch (error) {
            this.logger.error('Failed to create access control', error);
            return {
                success: false,
                message: `Failed to create access control: ${error.message}`,
            };
        }
    }
    async updateAccessControl(token, request) {
        try {
            const user = await this.authService.verifyToken(token);
            if (!user) {
                return {
                    success: false,
                    message: 'Authentication failed',
                };
            }
            this.logger.log(`Updating access control for file ${request.fileCid} by ${user.zkLoginAddress}`);
            const validationResult = this.validateAccessRule(request.accessRule);
            if (!validationResult.valid) {
                return {
                    success: false,
                    message: `Invalid access rule: ${validationResult.error}`,
                };
            }
            const transactionDigest = await this.suiService.updateFileAccessControl(user.zkLoginAddress, request.fileCid, request.accessRule);
            this.logger.log(`Access control updated for file ${request.fileCid}: ${transactionDigest}`);
            return {
                success: true,
                message: 'Access control updated successfully',
                transactionDigest,
            };
        }
        catch (error) {
            this.logger.error('Failed to update access control', error);
            return {
                success: false,
                message: `Failed to update access control: ${error.message}`,
            };
        }
    }
    async validateAccess(token, request) {
        try {
            const user = await this.authService.verifyToken(token);
            if (!user) {
                return {
                    success: false,
                    message: 'Authentication failed',
                    accessGranted: false,
                };
            }
            this.logger.log(`Validating access for file ${request.fileCid} by ${user.zkLoginAddress}`);
            const accessGranted = await this.suiService.validateFileAccess(request.fileCid, request.userAddress || user.zkLoginAddress, request.userEmail || user.email);
            this.logger.log(`Access validation result for file ${request.fileCid}: ${accessGranted}`);
            return {
                success: true,
                message: accessGranted ? 'Access granted' : 'Access denied',
                accessGranted,
            };
        }
        catch (error) {
            this.logger.error('Failed to validate access', error);
            return {
                success: false,
                message: `Failed to validate access: ${error.message}`,
                accessGranted: false,
            };
        }
    }
    async getAccessControlInfo(token, fileCid) {
        try {
            const user = await this.authService.verifyToken(token);
            if (!user) {
                return {
                    success: false,
                    message: 'Authentication failed',
                };
            }
            this.logger.log(`Getting access control info for file ${fileCid}`);
            const accessControlInfo = await this.suiService.getFileAccessControlInfo(fileCid);
            if (!accessControlInfo) {
                return {
                    success: false,
                    message: 'Access control not found for this file',
                };
            }
            return {
                success: true,
                data: accessControlInfo,
                message: 'Access control information retrieved successfully',
            };
        }
        catch (error) {
            this.logger.error('Failed to get access control info', error);
            return {
                success: false,
                message: `Failed to get access control info: ${error.message}`,
            };
        }
    }
    validateAccessRule(rule) {
        if (!['email', 'wallet', 'time', 'hybrid'].includes(rule.conditionType)) {
            return { valid: false, error: 'Invalid condition type' };
        }
        if (rule.allowedEmails && rule.allowedEmails.length > 0) {
            for (const email of rule.allowedEmails) {
                if (!this.isValidEmail(email)) {
                    return { valid: false, error: `Invalid email address: ${email}` };
                }
            }
        }
        if (rule.allowedAddresses && rule.allowedAddresses.length > 0) {
            for (let i = 0; i < rule.allowedAddresses.length; i++) {
                const address = rule.allowedAddresses[i];
                if (!(0, utils_1.isValidSuiAddress)(address)) {
                    return { valid: false, error: `Invalid Sui address: ${address}` };
                }
                rule.allowedAddresses[i] = (0, utils_1.normalizeSuiAddress)(address);
            }
        }
        if (rule.allowedSuiNS && rule.allowedSuiNS.length > 0) {
            for (const suiNS of rule.allowedSuiNS) {
                if (!this.isValidSuiNS(suiNS)) {
                    return { valid: false, error: `Invalid SuiNS name: ${suiNS}` };
                }
            }
        }
        if (rule.accessStartTime && rule.accessEndTime) {
            if (rule.accessStartTime >= rule.accessEndTime) {
                return { valid: false, error: 'Access start time must be before end time' };
            }
        }
        if (rule.maxAccessDuration && rule.maxAccessDuration <= 0) {
            return { valid: false, error: 'Max access duration must be positive' };
        }
        if (rule.maxAccessCount && rule.maxAccessCount <= 0) {
            return { valid: false, error: 'Max access count must be positive' };
        }
        if (rule.conditionType === 'hybrid') {
            const hasEmail = rule.allowedEmails && rule.allowedEmails.length > 0;
            const hasAddress = rule.allowedAddresses && rule.allowedAddresses.length > 0;
            const hasSuiNS = rule.allowedSuiNS && rule.allowedSuiNS.length > 0;
            const hasTime = rule.accessStartTime || rule.accessEndTime || rule.maxAccessDuration;
            if (!hasEmail && !hasAddress && !hasSuiNS && !hasTime) {
                return { valid: false, error: 'Hybrid access control must specify at least one access method' };
            }
        }
        return { valid: true };
    }
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    async resolveEmailToAddress(email) {
        try {
            this.logger.log(`Email to address resolution not implemented for: ${email}`);
            return null;
        }
        catch (error) {
            this.logger.error('Failed to resolve email to address', error);
            return null;
        }
    }
    async generateShareLink(token, request) {
        try {
            const user = await this.authService.verifyToken(token);
            if (!user) {
                return {
                    success: false,
                    message: 'Authentication failed',
                };
            }
            this.logger.log(`Generating share link for file ${request.fileCid} by ${user.zkLoginAddress}`);
            const shareId = `share_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            const shareLink = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/share/${shareId}`;
            const shareData = {
                shareId,
                fileCid: request.fileCid,
                createdBy: user.zkLoginAddress,
                createdAt: Date.now(),
                expirationTime: request.expirationTime,
                maxUses: request.maxUses,
                currentUses: 0,
            };
            this.shareLinks.set(shareId, shareData);
            this.logger.log(`Share link created: ${shareId} for file ${request.fileCid}`);
            return {
                success: true,
                data: {
                    shareLink,
                    shareId,
                    expirationTime: request.expirationTime,
                    maxUses: request.maxUses,
                },
                message: 'Share link generated successfully',
            };
        }
        catch (error) {
            this.logger.error('Failed to generate share link', error);
            return {
                success: false,
                message: `Failed to generate share link: ${error.message}`,
            };
        }
    }
    async generateShareLinkTest(request) {
        try {
            this.logger.log(`Generating share link (test mode) for file ${request.fileCid}`);
            const shareId = `test_share_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            const shareLink = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/share/${shareId}`;
            const shareData = {
                shareId,
                fileCid: request.fileCid,
                createdBy: 'test-user',
                createdAt: Date.now(),
                expirationTime: request.expirationTime,
                maxUses: request.maxUses,
                currentUses: 0,
            };
            this.shareLinks.set(shareId, shareData);
            this.logger.log(`Test share link created: ${shareId} for file ${request.fileCid}`);
            return {
                success: true,
                data: {
                    shareLink,
                    shareId,
                    expirationTime: request.expirationTime,
                    maxUses: request.maxUses,
                },
                message: 'Share link generated successfully (test mode)',
            };
        }
        catch (error) {
            this.logger.error('Failed to generate share link (test)', error);
            return {
                success: false,
                message: `Failed to generate share link: ${error.message}`,
            };
        }
    }
    async validateShareLink(shareId, token) {
        try {
            this.logger.log(`Validating share link: ${shareId}`);
            if (!shareId.startsWith('share_') && !shareId.startsWith('test_share_')) {
                return {
                    success: false,
                    message: 'Invalid share link',
                };
            }
            const shareData = this.shareLinks.get(shareId);
            if (!shareData) {
                return {
                    success: false,
                    message: 'Share link not found or has expired',
                };
            }
            if (shareData.expirationTime && Date.now() > shareData.expirationTime) {
                return {
                    success: false,
                    message: 'Share link has expired',
                };
            }
            if (shareData.maxUses && shareData.currentUses >= shareData.maxUses) {
                return {
                    success: false,
                    message: 'Share link usage limit exceeded',
                };
            }
            let fileMetadata = null;
            try {
                fileMetadata = await this.suiService.getFileMetadata(shareData.fileCid);
            }
            catch (error) {
                this.logger.warn(`Could not retrieve file metadata for ${shareData.fileCid}:`, error);
            }
            shareData.currentUses++;
            return {
                success: true,
                data: {
                    fileCid: shareData.fileCid,
                    accessGranted: true,
                    filename: fileMetadata?.filename || 'shared-file',
                    fileSize: fileMetadata?.fileSize,
                    contentType: 'application/octet-stream',
                    isEncrypted: false,
                },
                message: 'Share link validated successfully',
            };
        }
        catch (error) {
            this.logger.error('Failed to validate share link', error);
            return {
                success: false,
                message: `Failed to validate share link: ${error.message}`,
            };
        }
    }
    async processBulkUpload(token, file, fileCid, conditionType) {
        try {
            const user = await this.authService.verifyToken(token);
            if (!user) {
                return {
                    success: false,
                    message: 'Authentication failed',
                };
            }
            this.logger.log(`Processing bulk upload for file ${fileCid} by ${user.zkLoginAddress}`);
            const allowedMimeTypes = [
                'text/csv',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ];
            if (!allowedMimeTypes.includes(file.mimetype)) {
                return {
                    success: false,
                    message: 'Invalid file type. Only CSV and Excel files are supported.',
                };
            }
            const fileContent = file.buffer.toString('utf-8');
            const parsedData = this.parseBulkData(fileContent, file.mimetype);
            if (parsedData.errors.length > 0) {
                return {
                    success: false,
                    message: `Validation errors: ${parsedData.errors.join(', ')}`,
                    data: { errors: parsedData.errors },
                };
            }
            const accessRule = {
                conditionType,
                allowedEmails: parsedData.emails.length > 0 ? parsedData.emails : undefined,
                allowedAddresses: parsedData.addresses.length > 0 ? parsedData.addresses : undefined,
                allowedSuiNS: parsedData.suiNSNames.length > 0 ? parsedData.suiNSNames : undefined,
            };
            const validation = this.validateAccessRule(accessRule);
            if (!validation.valid) {
                return {
                    success: false,
                    message: validation.error || 'Invalid access rule',
                };
            }
            const updateResult = await this.updateAccessControl(token, {
                fileCid,
                accessRule,
            });
            if (!updateResult.success) {
                return {
                    success: false,
                    message: updateResult.message,
                };
            }
            return {
                success: true,
                data: {
                    processed: {
                        emails: parsedData.emails.length,
                        addresses: parsedData.addresses.length,
                        suiNSNames: parsedData.suiNSNames.length,
                    },
                    transactionDigest: updateResult.transactionDigest,
                },
                message: `Bulk upload processed successfully. Added ${parsedData.emails.length} emails, ${parsedData.addresses.length} addresses, and ${parsedData.suiNSNames.length} SuiNS names.`,
            };
        }
        catch (error) {
            this.logger.error('Failed to process bulk upload', error);
            return {
                success: false,
                message: `Failed to process bulk upload: ${error.message}`,
            };
        }
    }
    parseBulkData(content, mimeType) {
        const result = {
            emails: [],
            addresses: [],
            suiNSNames: [],
            errors: [],
        };
        try {
            let lines = [];
            if (mimeType === 'text/csv') {
                lines = content.split('\n').map(line => line.trim()).filter(line => line);
            }
            else {
                lines = content.split('\n').map(line => line.trim()).filter(line => line);
            }
            const hasHeader = lines.length > 0 && (lines[0].toLowerCase().includes('email') ||
                lines[0].toLowerCase().includes('address') ||
                lines[0].toLowerCase().includes('suins'));
            const dataLines = hasHeader ? lines.slice(1) : lines;
            for (const line of dataLines) {
                if (!line)
                    continue;
                const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
                for (const value of values) {
                    if (!value)
                        continue;
                    if (this.isValidEmail(value)) {
                        if (!result.emails.includes(value)) {
                            result.emails.push(value);
                        }
                    }
                    else if ((0, utils_1.isValidSuiAddress)(value)) {
                        if (!result.addresses.includes(value)) {
                            result.addresses.push(value);
                        }
                    }
                    else if (this.isValidSuiNS(value)) {
                        if (!result.suiNSNames.includes(value)) {
                            result.suiNSNames.push(value);
                        }
                    }
                    else {
                        result.errors.push(`Invalid format: ${value}`);
                    }
                }
            }
            const maxEntries = 1000;
            if (result.emails.length > maxEntries) {
                result.errors.push(`Too many emails (max ${maxEntries})`);
                result.emails = result.emails.slice(0, maxEntries);
            }
            if (result.addresses.length > maxEntries) {
                result.errors.push(`Too many addresses (max ${maxEntries})`);
                result.addresses = result.addresses.slice(0, maxEntries);
            }
            if (result.suiNSNames.length > maxEntries) {
                result.errors.push(`Too many SuiNS names (max ${maxEntries})`);
                result.suiNSNames = result.suiNSNames.slice(0, maxEntries);
            }
        }
        catch (error) {
            result.errors.push(`Failed to parse file: ${error.message}`);
        }
        return result;
    }
    isValidSuiNS(name) {
        const suiNSRegex = /^[a-zA-Z0-9-_]+\.sui$/;
        return suiNSRegex.test(name);
    }
};
exports.AccessControlService = AccessControlService;
exports.AccessControlService = AccessControlService = AccessControlService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [sui_service_1.SuiService,
        auth_service_1.AuthService])
], AccessControlService);
//# sourceMappingURL=access-control.service.js.map
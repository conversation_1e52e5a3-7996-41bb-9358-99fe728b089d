"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.oauthProviderConfigs = exports.OAuthProvider = exports.defaultZkLoginConfig = void 0;
exports.validateZkLoginConfig = validateZkLoginConfig;
exports.defaultZkLoginConfig = {
    oauth: {
        google: {
            clientId: process.env.GOOGLE_CLIENT_ID || '',
            redirectUri: process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3000/auth/google/callback',
        },
        facebook: {
            clientId: process.env.FACEBOOK_CLIENT_ID || '',
            redirectUri: process.env.FACEBOOK_REDIRECT_URI || 'http://localhost:3000/auth/facebook/callback',
        },
        twitch: {
            clientId: process.env.TWITCH_CLIENT_ID || '',
            redirectUri: process.env.TWITCH_REDIRECT_URI || 'http://localhost:3000/auth/twitch/callback',
        },
        apple: {
            clientId: process.env.APPLE_CLIENT_ID || '',
            redirectUri: process.env.APPLE_REDIRECT_URI || 'http://localhost:3000/auth/apple/callback',
        },
        github: {
            clientId: process.env.GITHUB_CLIENT_ID || '',
            redirectUri: process.env.GITHUB_REDIRECT_URI || 'http://localhost:5173/',
        },
    },
    sui: {
        network: process.env.SUI_NETWORK || 'testnet',
        rpcUrl: process.env.SUI_RPC_URL || 'https://fullnode.testnet.sui.io:443',
        packageId: process.env.SUICIRCLE_PACKAGE_ID,
        registryId: process.env.SUICIRCLE_REGISTRY_ID,
    },
    zkLogin: {
        salt: process.env.ZKLOGIN_SALT || 'default-salt-change-in-production',
        maxEpoch: parseInt(process.env.ZKLOGIN_MAX_EPOCH || '10'),
        proverUrl: process.env.ZKLOGIN_PROVER_URL || 'https://prover-dev.mystenlabs.com/v1',
    },
    jwt: {
        secret: process.env.JWT_SECRET || 'your-jwt-secret-change-in-production',
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    },
};
console.log('=== zkLogin Config Debug ===');
console.log('GITHUB_CLIENT_ID:', process.env.GITHUB_CLIENT_ID);
console.log('GITHUB_REDIRECT_URI:', process.env.GITHUB_REDIRECT_URI);
console.log('GitHub config:', exports.defaultZkLoginConfig.oauth.github);
console.log('===========================');
var OAuthProvider;
(function (OAuthProvider) {
    OAuthProvider["GOOGLE"] = "google";
    OAuthProvider["FACEBOOK"] = "facebook";
    OAuthProvider["TWITCH"] = "twitch";
    OAuthProvider["APPLE"] = "apple";
    OAuthProvider["GITHUB"] = "github";
})(OAuthProvider || (exports.OAuthProvider = OAuthProvider = {}));
exports.oauthProviderConfigs = {
    [OAuthProvider.GOOGLE]: {
        authUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
        tokenUrl: 'https://oauth2.googleapis.com/token',
        userInfoUrl: 'https://www.googleapis.com/oauth2/v2/userinfo',
        scope: 'openid email profile',
    },
    [OAuthProvider.FACEBOOK]: {
        authUrl: 'https://www.facebook.com/v18.0/dialog/oauth',
        tokenUrl: 'https://graph.facebook.com/v18.0/oauth/access_token',
        userInfoUrl: 'https://graph.facebook.com/me',
        scope: 'email',
    },
    [OAuthProvider.TWITCH]: {
        authUrl: 'https://id.twitch.tv/oauth2/authorize',
        tokenUrl: 'https://id.twitch.tv/oauth2/token',
        userInfoUrl: 'https://api.twitch.tv/helix/users',
        scope: 'openid user:read:email',
    },
    [OAuthProvider.APPLE]: {
        authUrl: 'https://appleid.apple.com/auth/authorize',
        tokenUrl: 'https://appleid.apple.com/auth/token',
        userInfoUrl: '',
        scope: 'name email',
    },
    [OAuthProvider.GITHUB]: {
        authUrl: 'https://github.com/login/oauth/authorize',
        tokenUrl: 'https://github.com/login/oauth/access_token',
        userInfoUrl: 'https://api.github.com/user',
        scope: 'user:email',
    },
};
function validateZkLoginConfig(config) {
    const errors = [];
    Object.entries(config.oauth).forEach(([provider, settings]) => {
        if (!settings.clientId) {
            errors.push(`Missing client ID for ${provider}`);
        }
        if (!settings.redirectUri) {
            errors.push(`Missing redirect URI for ${provider}`);
        }
    });
    if (!config.sui.rpcUrl) {
        errors.push('Missing Sui RPC URL');
    }
    if (!config.zkLogin.salt) {
        errors.push('Missing zkLogin salt');
    }
    if (config.zkLogin.salt === 'default-salt-change-in-production') {
        console.warn('WARNING: Using default salt in production is not secure!');
    }
    if (!config.jwt.secret) {
        errors.push('Missing JWT secret');
    }
    if (config.jwt.secret === 'your-jwt-secret-change-in-production') {
        console.warn('WARNING: Using default JWT secret in production is not secure!');
    }
    if (errors.length > 0) {
        throw new Error(`zkLogin configuration errors: ${errors.join(', ')}`);
    }
}
//# sourceMappingURL=zklogin.config.js.map
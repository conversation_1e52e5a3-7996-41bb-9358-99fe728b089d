{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\crypto\\vdf.move", "definition_location": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 90, "end": 93}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "vdf"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 319, "end": 419}, "definition_location": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 330, "end": 343}, "type_parameters": [], "parameters": [["message#0#0", {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 344, "end": 351}]], "returns": [{"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 367, "end": 377}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 408, "end": 415}, "1": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 385, "end": 416}}, "is_native": false}, "1": {"location": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 472, "end": 540}, "definition_location": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 483, "end": 505}, "type_parameters": [], "parameters": [["message#0#0", {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 506, "end": 513}]], "returns": [{"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 529, "end": 539}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "2": {"location": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1492, "end": 1685}, "definition_location": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1503, "end": 1513}, "type_parameters": [], "parameters": [["input#0#0", {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1520, "end": 1525}], ["output#0#0", {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1545, "end": 1551}], ["proof#0#0", {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1571, "end": 1576}], ["iterations#0#0", {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1596, "end": 1606}]], "returns": [{"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1617, "end": 1621}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1649, "end": 1654}, "1": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1656, "end": 1662}, "2": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1664, "end": 1669}, "3": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1671, "end": 1681}, "4": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1629, "end": 1682}}, "is_native": false}, "3": {"location": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1744, "end": 1883}, "definition_location": {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1755, "end": 1774}, "type_parameters": [], "parameters": [["input#0#0", {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1781, "end": 1786}], ["output#0#0", {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1806, "end": 1812}], ["proof#0#0", {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1832, "end": 1837}], ["iterations#0#0", {"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1857, "end": 1867}]], "returns": [{"file_hash": [192, 38, 218, 167, 169, 35, 124, 147, 237, 246, 100, 41, 196, 185, 72, 122, 204, 226, 182, 91, 250, 79, 4, 214, 69, 44, 29, 48, 235, 94, 198, 119], "start": 1878, "end": 1882}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EInvalidInput": 0}}
{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\tx_context.move", "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 90, "end": 100}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "tx_context"], "struct_map": {"0": {"definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 620, "end": 629}, "type_parameters": [], "fields": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 715, "end": 721}, {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 778, "end": 785}, {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 838, "end": 843}, {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 900, "end": 918}, {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1072, "end": 1083}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1173, "end": 1244}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1184, "end": 1190}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1191, "end": 1196}]], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1211, "end": 1218}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1226, "end": 1241}}, "is_native": false}, "1": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1246, "end": 1282}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1257, "end": 1270}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1274, "end": 1281}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "2": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1401, "end": 1473}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1412, "end": 1418}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1419, "end": 1423}]], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1438, "end": 1449}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1458, "end": 1462}, "1": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1457, "end": 1470}}, "is_native": false}, "3": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1507, "end": 1572}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1518, "end": 1523}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1524, "end": 1529}]], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1544, "end": 1547}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1555, "end": 1569}}, "is_native": false}, "4": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1574, "end": 1605}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1585, "end": 1597}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1601, "end": 1604}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "5": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1679, "end": 1770}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1690, "end": 1708}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1709, "end": 1714}]], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1729, "end": 1732}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1740, "end": 1767}}, "is_native": false}, "6": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1772, "end": 1816}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1783, "end": 1808}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1812, "end": 1815}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "7": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1905, "end": 1986}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1916, "end": 1923}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1924, "end": 1929}]], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1944, "end": 1959}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 1967, "end": 1983}}, "is_native": false}, "8": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2194, "end": 2277}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2205, "end": 2225}, "type_parameters": [], "parameters": [["_ctx#0#0", {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2226, "end": 2230}]], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2249, "end": 2256}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2264, "end": 2274}}, "is_native": false}, "9": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2279, "end": 2310}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2290, "end": 2298}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2302, "end": 2309}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "10": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2417, "end": 2494}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2428, "end": 2447}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2448, "end": 2453}]], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2468, "end": 2471}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2479, "end": 2491}}, "is_native": false}, "11": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2496, "end": 2525}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2507, "end": 2517}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2521, "end": 2524}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "12": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2663, "end": 2736}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2674, "end": 2683}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2684, "end": 2689}]], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2704, "end": 2707}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2715, "end": 2733}}, "is_native": false}, "13": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2738, "end": 2773}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2749, "end": 2765}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2769, "end": 2772}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "14": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 2998, "end": 3035}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 3009, "end": 3027}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 3031, "end": 3034}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "15": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 3279, "end": 3315}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 3290, "end": 3307}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 3311, "end": 3314}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "16": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6568, "end": 6724}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6572, "end": 6586}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6590, "end": 6605}], "locals": [["%#1", {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6650, "end": 6721}], ["sponsor#1#0", {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6617, "end": 6624}]], "nops": {}, "code_map": {"0": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6627, "end": 6643}, "1": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6617, "end": 6624}, "2": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6654, "end": 6661}, "3": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6654, "end": 6670}, "4": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6674, "end": 6675}, "5": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6671, "end": 6673}, "6": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6650, "end": 6721}, "7": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6677, "end": 6691}, "8": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6650, "end": 6721}, "10": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6710, "end": 6720}, "11": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6718, "end": 6719}, "12": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6710, "end": 6720}, "14": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6697, "end": 6721}, "15": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6650, "end": 6721}}, "is_native": false}, "17": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6726, "end": 6771}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6737, "end": 6751}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 6755, "end": 6770}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "18": {"location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 7123, "end": 7192}, "definition_location": {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 7134, "end": 7143}, "type_parameters": [], "parameters": [["tx_hash#0#0", {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 7144, "end": 7151}], ["ids_created#0#0", {"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 7165, "end": 7176}]], "returns": [{"file_hash": [136, 93, 45, 30, 212, 171, 185, 127, 97, 213, 60, 124, 140, 151, 99, 254, 27, 36, 10, 82, 108, 180, 44, 33, 200, 1, 76, 128, 23, 121, 137, 165], "start": 7184, "end": 7191}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}
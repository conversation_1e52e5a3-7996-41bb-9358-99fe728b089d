import { Injectable, Logger } from '@nestjs/common';
import { WalrusClient, WalrusFile } from '@mysten/walrus';
import { SuiClient, getFullnodeUrl } from '@mysten/sui/client';
import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';

export interface WalrusUploadResult {
  success: boolean;
  blobId?: string;
  error?: string;
  size?: number;
}

export interface WalrusDownloadResult {
  success: boolean;
  data?: Uint8Array;
  error?: string;
}

@Injectable()
export class WalrusService {
  private readonly logger = new Logger(WalrusService.name);
  private walrusClient: WalrusClient;
  private suiClient: SuiClient;
  private signer: Ed25519Keypair | null = null;
  private useUploadRelay: boolean;

  constructor() {
    this.initializeClients();
  }

  private initializeClients() {
    try {
      const network = process.env.WALRUS_NETWORK || 'testnet';
      const suiRpcUrl = process.env.SUI_RPC_URL || getFullnodeUrl('testnet');
      this.useUploadRelay = process.env.WALRUS_USE_UPLOAD_RELAY === 'true';

      // Initialize Sui client
      this.suiClient = new SuiClient({
        url: suiRpcUrl,
      });

      // Initialize signer if private key is provided or if using upload relay
      if (process.env.WALRUS_PRIVATE_KEY) {
        try {
          this.signer = Ed25519Keypair.fromSecretKey(process.env.WALRUS_PRIVATE_KEY);
          this.logger.log('Walrus signer initialized successfully');
        } catch (error) {
          this.logger.error('Failed to initialize Walrus signer:', error);
          throw new Error('Invalid WALRUS_PRIVATE_KEY format');
        }
      } else if (this.useUploadRelay) {
        // For upload relay, create a dummy signer 
        this.signer = new Ed25519Keypair();
        this.logger.log('Dummy signer created for upload relay mode');
      }

      // Initialize Walrus client
      const walrusConfig: any = {
        network: network as 'testnet' | 'mainnet',
        suiClient: this.suiClient,
        storageNodeClientOptions: {
          timeout: 60_000,
          onError: (error) => {
            this.logger.warn('Walrus storage node error:', error.message);
          },
        },
      };

      // Add upload relay configuration if enabled
      if (this.useUploadRelay) {
        const relayUrl = process.env.WALRUS_UPLOAD_RELAY_URL;
        const maxTip = parseInt(process.env.WALRUS_MAX_TIP || '1000');

        if (!relayUrl) {
          throw new Error('WALRUS_UPLOAD_RELAY_URL is required when using upload relay');
        }

        walrusConfig.uploadRelay = {
          host: relayUrl,
          sendTip: {
            max: maxTip,
          },
        };

        this.logger.log(`Walrus upload relay configured: ${relayUrl}`);
      }

      this.walrusClient = new WalrusClient(walrusConfig);

      this.logger.log(`Walrus client initialized successfully (${this.useUploadRelay ? 'upload relay' : 'direct upload'} mode)`);
    } catch (error) {
      this.logger.error('Failed to initialize Walrus clients:', error);
      throw error;
    }
  }

  /**
   * Upload a file to Walrus storage
   * @param fileData - The file data as Buffer or Uint8Array
   * @param filename - Original filename for metadata
   * @param contentType - MIME type of the file
   * @returns Promise with upload result containing blobId
   */
  async uploadFile(
    fileData: Buffer | Uint8Array,
    filename: string,
    contentType?: string
  ): Promise<WalrusUploadResult> {
    try {
      this.logger.log(`Starting upload for file: ${filename}, size: ${fileData.length} bytes`);

      // Convert Buffer to Uint8Array if needed
      const data = fileData instanceof Buffer ? new Uint8Array(fileData) : fileData;

      // Check if we're in development mode (fallback to mock)
      if (process.env.NODE_ENV === 'development' && !process.env.WALRUS_PRIVATE_KEY && !this.useUploadRelay) {
        return this.uploadFileMock(data, filename);
      }

      let result;

      if (this.useUploadRelay) {
        // Option 1: Upload via relay
        result = await this.uploadViaRelay(data, filename, contentType);
      } else {
        // Option 2: Direct upload with signer
        result = await this.uploadDirect(data, filename, contentType);
      }

      if (result.success) {
        this.logger.log(`✅ File uploaded successfully: ${filename} -> ${result.blobId}`);
        this.logger.log(`📊 File size: ${data.length} bytes`);
        this.logger.log(`🔗 Walrus CID: ${result.blobId}`);
      }

      return result;

    } catch (error) {
      this.logger.error(`Failed to upload file ${filename}:`, error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Upload file via upload relay
   */
  private async uploadViaRelay(
    data: Uint8Array,
    filename: string,
    contentType?: string
  ): Promise<WalrusUploadResult> {
    try {
      // Create WalrusFile with metadata
      const walrusFile = WalrusFile.from({
        contents: data,
        identifier: filename,
        tags: {
          'content-type': contentType || 'application/octet-stream',
          'upload-timestamp': new Date().toISOString(),
        },
      });

      // Upload via relay (signer is required by API but relay handles actual signing)
      if (!this.signer) {
        throw new Error('No signer available for upload relay');
      }

      const results = await this.walrusClient.writeFiles({
        files: [walrusFile],
        epochs: parseInt(process.env.WALRUS_STORAGE_EPOCHS || '5'),
        deletable: true,
        signer: this.signer,
      });

      if (results && results.length > 0) {
        return {
          success: true,
          blobId: results[0].blobId,
          size: data.length,
        };
      } else {
        throw new Error('No results returned from upload relay');
      }
    } catch (error) {
      this.logger.error('Upload relay failed:', error);
      return {
        success: false,
        error: `Upload relay failed: ${error.message}`,
      };
    }
  }

  /**
   * Upload file directly with signer
   */
  private async uploadDirect(
    data: Uint8Array,
    filename: string,
    contentType?: string
  ): Promise<WalrusUploadResult> {
    try {
      if (!this.signer) {
        throw new Error('No signer configured for direct upload. Set WALRUS_PRIVATE_KEY or enable upload relay.');
      }

      // Upload directly to Walrus
      const result = await this.walrusClient.writeBlob({
        blob: data,
        deletable: true,
        epochs: parseInt(process.env.WALRUS_STORAGE_EPOCHS || '5'),
        signer: this.signer,
      });

      return {
        success: true,
        blobId: result.blobId,
        size: data.length,
      };
    } catch (error) {
      this.logger.error('Direct upload failed:', error);
      return {
        success: false,
        error: `Direct upload failed: ${error.message}`,
      };
    }
  }

  /**
   * Fallback mock upload for development
   */
  private async uploadFileMock(
    data: Uint8Array,
    filename: string
  ): Promise<WalrusUploadResult> {
    this.logger.warn('Using mock upload - configure WALRUS_PRIVATE_KEY or upload relay for production');

    // Simulate upload delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const simulatedBlobId = this.generateMockBlobId(filename, data.length);

    this.logger.log(`✅ File upload simulated: ${filename} -> ${simulatedBlobId}`);

    return {
      success: true,
      blobId: simulatedBlobId,
      size: data.length,
    };
  }

  /**
   * Generate a mock blob ID for testing purposes
   * In production, this would be returned by Walrus
   */
  private generateMockBlobId(filename: string, size: number): string {
    const timestamp = Date.now();
    const hash = Buffer.from(`${filename}-${size}-${timestamp}`).toString('base64url');
    return `mock_${hash.substring(0, 32)}`;
  }

  /**
   * Validate Walrus configuration
   */
  public validateConfiguration(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check if we're in production mode
    const isProduction = process.env.NODE_ENV === 'production';

    if (isProduction) {
      // In production, we need either a signer or upload relay
      if (!this.useUploadRelay && !this.signer) {
        errors.push('Production mode requires either WALRUS_PRIVATE_KEY or WALRUS_USE_UPLOAD_RELAY=true');
      }

      if (this.useUploadRelay && !process.env.WALRUS_UPLOAD_RELAY_URL) {
        errors.push('Upload relay mode requires WALRUS_UPLOAD_RELAY_URL');
      }
    }

    // Check network configuration
    if (!process.env.SUI_RPC_URL) {
      errors.push('SUI_RPC_URL is required');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get current configuration status
   */
  public getConfigurationStatus(): {
    mode: 'mock' | 'upload-relay' | 'direct-upload';
    network: string;
    hasPrivateKey: boolean;
    uploadRelayUrl?: string;
  } {
    return {
      mode: this.useUploadRelay ? 'upload-relay' : (this.signer ? 'direct-upload' : 'mock'),
      network: process.env.WALRUS_NETWORK || 'testnet',
      hasPrivateKey: !!this.signer,
      uploadRelayUrl: this.useUploadRelay ? process.env.WALRUS_UPLOAD_RELAY_URL : undefined,
    };
  }

  /**
   * Get wallet address and balance information
   */
  public async getWalletInfo(): Promise<{
    address?: string;
    suiBalance?: string;
    walBalance?: string;
    error?: string;
  }> {
    try {
      if (!this.signer) {
        return { error: 'No signer available' };
      }

      const address = this.signer.toSuiAddress();

      // Get SUI balance
      const suiBalance = await this.suiClient.getBalance({
        owner: address,
        coinType: '0x2::sui::SUI',
      });

      // Get WAL balance (WAL token type for testnet)
      const walTokenType = '0x8270feb7375eee355e64fdb69c50abb6b5f9393a722883c1cf45f8e26048810a::wal::WAL';
      let walBalance;
      try {
        walBalance = await this.suiClient.getBalance({
          owner: address,
          coinType: walTokenType,
        });
      } catch (error) {
        // WAL balance might not exist if no WAL tokens
        walBalance = { totalBalance: '0', coinType: walTokenType, coinObjectCount: 0 };
      }

      return {
        address,
        suiBalance: suiBalance.totalBalance,
        walBalance: walBalance.totalBalance,
      };
    } catch (error) {
      this.logger.error('Failed to get wallet info:', error);
      return { error: error.message };
    }
  }

  /**
   * Download a file from Walrus storage
   * @param blobId - The Walrus blob ID
   * @returns Promise with download result containing file data
   */
  async downloadFile(blobId: string): Promise<WalrusDownloadResult> {
    try {
      this.logger.log(`Downloading file with blobId: ${blobId}`);

      // Check if this is a mock blob ID (fallback for development)
      if (blobId.startsWith('mock_')) {
        return this.downloadFileMock(blobId);
      }

      // Download from real Walrus storage
      const data = await this.walrusClient.readBlob({ blobId });

      this.logger.log(`✅ File downloaded successfully, size: ${data.length} bytes`);

      return {
        success: true,
        data,
      };
    } catch (error) {
      this.logger.error(`Failed to download file with blobId ${blobId}:`, error);

      // If it's a network error, provide helpful message
      if (error.message.includes('network') || error.message.includes('timeout')) {
        return {
          success: false,
          error: `Network error downloading from Walrus: ${error.message}. Please check your connection and try again.`,
        };
      }

      // If blob not found
      if (error.message.includes('not found') || error.message.includes('404')) {
        return {
          success: false,
          error: `File not found in Walrus storage. The blob ID may be invalid or the file may have expired.`,
        };
      }

      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Download mock file for development
   */
  private async downloadFileMock(blobId: string): Promise<WalrusDownloadResult> {
    this.logger.warn('Downloading mock file - this is development mode');

    // Simulate download delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    // Generate mock file content
    const mockContent = `Mock file content for ${blobId}
Generated at: ${new Date().toISOString()}
This is simulated data from Walrus storage.
Original blob ID: ${blobId}
File content would be retrieved from Walrus network.`;

    const data = new Uint8Array(Buffer.from(mockContent, 'utf-8'));

    this.logger.log(`✅ Mock file downloaded successfully, size: ${data.length} bytes`);

    return {
      success: true,
      data,
    };
  }

  /**
   * Check if a blob exists in Walrus storage
   * @param blobId - The Walrus blob ID
   * @returns Promise<boolean> indicating if blob exists
   */
  async blobExists(blobId: string): Promise<boolean> {
    try {
      await this.walrusClient.readBlob({ blobId });
      return true;
    } catch (error) {
      this.logger.debug(`Blob ${blobId} does not exist or is not accessible`);
      return false;
    }
  }

  /**
   * Get blob information without downloading the full content
   * @param blobId - The Walrus blob ID
   * @returns Promise with blob metadata
   */
  async getBlobInfo(blobId: string): Promise<{
    exists: boolean;
    size?: number;
    error?: string;
  }> {
    try {
      // For now, we'll try to read the blob to check if it exists
      // In a production setup, you might want to use a more efficient method
      const data = await this.walrusClient.readBlob({ blobId });

      return {
        exists: true,
        size: data.length,
      };
    } catch (error) {
      return {
        exists: false,
        error: error.message,
      };
    }
  }
}

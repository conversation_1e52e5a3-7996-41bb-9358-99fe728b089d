{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\transfer.move", "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 114, "end": 122}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "transfer"], "struct_map": {"0": {"definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 611, "end": 620}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 629, "end": 630}]], "fields": [{"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 653, "end": 655}, {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 666, "end": 673}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2402, "end": 2497}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2413, "end": 2421}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2422, "end": 2423}]], "parameters": [["obj#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2430, "end": 2433}], ["recipient#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2438, "end": 2447}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2479, "end": 2482}, "1": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2484, "end": 2493}, "2": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2465, "end": 2494}}, "is_native": false}, "1": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2933, "end": 3043}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2944, "end": 2959}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2960, "end": 2961}]], "parameters": [["obj#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2976, "end": 2979}], ["recipient#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 2984, "end": 2993}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 3025, "end": 3028}, "1": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 3030, "end": 3039}, "2": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 3011, "end": 3040}}, "is_native": false}, "2": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4063, "end": 4329}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4074, "end": 4088}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4089, "end": 4090}]], "parameters": [["obj#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4097, "end": 4100}], ["party#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4105, "end": 4110}]], "returns": [], "locals": [["addresses#1#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4217, "end": 4226}], ["default#1#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4208, "end": 4215}], ["permissions#1#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4228, "end": 4239}]], "nops": {}, "code_map": {"0": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4146, "end": 4151}, "1": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4146, "end": 4169}, "2": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4138, "end": 4196}, "4": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4171, "end": 4195}, "5": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4138, "end": 4196}, "6": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4243, "end": 4248}, "7": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4243, "end": 4262}, "8": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4228, "end": 4239}, "9": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4217, "end": 4226}, "10": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4208, "end": 4215}, "11": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4289, "end": 4292}, "12": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4294, "end": 4301}, "13": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4303, "end": 4312}, "14": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4314, "end": 4325}, "15": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 4269, "end": 4326}}, "is_native": false}, "3": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5162, "end": 5443}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5173, "end": 5194}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5195, "end": 5196}]], "parameters": [["obj#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5211, "end": 5214}], ["party#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5219, "end": 5224}]], "returns": [], "locals": [["addresses#1#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5331, "end": 5340}], ["default#1#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5322, "end": 5329}], ["permissions#1#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5342, "end": 5353}]], "nops": {}, "code_map": {"0": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5260, "end": 5265}, "1": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5260, "end": 5283}, "2": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5252, "end": 5310}, "4": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5285, "end": 5309}, "5": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5252, "end": 5310}, "6": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5357, "end": 5362}, "7": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5357, "end": 5376}, "8": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5342, "end": 5353}, "9": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5331, "end": 5340}, "10": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5322, "end": 5329}, "11": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5403, "end": 5406}, "12": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5408, "end": 5415}, "13": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5417, "end": 5426}, "14": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5428, "end": 5439}, "15": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5383, "end": 5440}}, "is_native": false}, "4": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5819, "end": 5893}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5830, "end": 5843}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5844, "end": 5845}]], "parameters": [["obj#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5852, "end": 5855}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5886, "end": 5889}, "1": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 5867, "end": 5890}}, "is_native": false}, "5": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 6075, "end": 6164}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 6086, "end": 6106}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 6107, "end": 6108}]], "parameters": [["obj#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 6123, "end": 6126}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 6157, "end": 6160}, "1": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 6138, "end": 6161}}, "is_native": false}, "6": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 6764, "end": 6836}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 6775, "end": 6787}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 6788, "end": 6789}]], "parameters": [["obj#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 6796, "end": 6799}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 6829, "end": 6832}, "1": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 6811, "end": 6833}}, "is_native": false}, "7": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7245, "end": 7332}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7256, "end": 7275}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7276, "end": 7277}]], "parameters": [["obj#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7292, "end": 7295}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7325, "end": 7328}, "1": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7307, "end": 7329}}, "is_native": false}, "8": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7829, "end": 8008}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7840, "end": 7847}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7848, "end": 7849}]], "parameters": [["parent#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7856, "end": 7862}], ["to_receive#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7874, "end": 7884}]], "returns": [{"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7901, "end": 7902}], "locals": [["id#1#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7926, "end": 7928}], ["version#1#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7930, "end": 7937}]], "nops": {}, "code_map": {"0": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7942, "end": 7952}, "1": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7914, "end": 7939}, "2": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7930, "end": 7937}, "3": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7926, "end": 7928}, "4": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7972, "end": 7978}, "6": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7972, "end": 7991}, "7": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7993, "end": 7995}, "8": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7997, "end": 8004}, "9": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 7959, "end": 8005}}, "is_native": false}, "9": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8332, "end": 8526}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8343, "end": 8357}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8358, "end": 8359}]], "parameters": [["parent#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8374, "end": 8380}], ["to_receive#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8392, "end": 8402}]], "returns": [{"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8419, "end": 8420}], "locals": [["id#1#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8444, "end": 8446}], ["version#1#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8448, "end": 8455}]], "nops": {}, "code_map": {"0": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8460, "end": 8470}, "1": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8432, "end": 8457}, "2": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8448, "end": 8455}, "3": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8444, "end": 8446}, "4": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8490, "end": 8496}, "6": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8490, "end": 8509}, "7": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8511, "end": 8513}, "8": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8515, "end": 8522}, "9": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8477, "end": 8523}}, "is_native": false}, "10": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8604, "end": 8695}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8615, "end": 8634}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8635, "end": 8636}]], "parameters": [["receiving#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8643, "end": 8652}]], "returns": [{"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8670, "end": 8672}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8680, "end": 8689}, "1": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8680, "end": 8692}}, "is_native": false}, "11": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8699, "end": 8761}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8726, "end": 8744}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8745, "end": 8746}]], "parameters": [["obj#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8753, "end": 8756}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "12": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8765, "end": 8826}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8792, "end": 8809}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8810, "end": 8811}]], "parameters": [["obj#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8818, "end": 8821}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "13": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8830, "end": 8997}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8857, "end": 8876}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8877, "end": 8878}]], "parameters": [["obj#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8891, "end": 8894}], ["default_permissions#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8904, "end": 8923}], ["addresses#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8935, "end": 8944}], ["permissions#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 8968, "end": 8979}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "14": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 9001, "end": 9078}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 9028, "end": 9041}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 9042, "end": 9043}]], "parameters": [["obj#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 9050, "end": 9053}], ["recipient#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 9058, "end": 9067}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "15": {"location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 9082, "end": 9164}, "definition_location": {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 9093, "end": 9105}, "type_parameters": [["T", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 9106, "end": 9107}]], "parameters": [["parent#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 9114, "end": 9120}], ["to_receive#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 9131, "end": 9141}], ["version#0#0", {"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 9147, "end": 9154}]], "returns": [{"file_hash": [109, 214, 117, 129, 125, 1, 197, 15, 201, 118, 229, 63, 185, 175, 116, 20, 234, 52, 154, 137, 48, 255, 75, 28, 189, 154, 58, 224, 58, 31, 67, 193], "start": 9162, "end": 9163}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EBCSSerializationFailure": 1, "EInvalidPartyPermissions": 7, "ENotSupported": 5, "EReceivingObjectTypeMismatch": 2, "ESharedNonNewObject": 0, "ESharedObjectOperationNotSupported": 4, "EUnableToReceiveObject": 3}}
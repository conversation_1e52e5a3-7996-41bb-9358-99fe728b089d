"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AppController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppController = void 0;
const common_1 = require("@nestjs/common");
const app_service_1 = require("./app.service");
const file_service_1 = require("./file/file.service");
const auth_guard_1 = require("./auth/auth.guard");
let AppController = AppController_1 = class AppController {
    appService;
    fileService;
    logger = new common_1.Logger(AppController_1.name);
    constructor(appService, fileService) {
        this.appService = appService;
        this.fileService = fileService;
    }
    getHello() {
        return this.appService.getHello();
    }
    async listUserFiles(authorization, user) {
        try {
            const token = authorization.substring(7);
            const result = await this.fileService.listUserFiles(token);
            return {
                success: result.success,
                files: result.files,
                data: {
                    files: result.files,
                },
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to list user files', error);
            throw new common_1.HttpException('Failed to list user files', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async listUserFilesTest() {
        try {
            const result = await this.fileService.listUserFilesNoAuth();
            return {
                success: result.success,
                files: result.files,
                data: {
                    files: result.files,
                },
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to list user files (test)', error);
            throw new common_1.HttpException('Failed to list user files', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async clearUserFiles(authorization, user) {
        try {
            const token = authorization.substring(7);
            const result = await this.fileService.clearUserFiles(token);
            return {
                success: result.success,
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to clear user files', error);
            throw new common_1.HttpException('Failed to clear user files', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async clearUserFilesTest() {
        try {
            const result = await this.fileService.clearUserFilesNoAuth();
            return {
                success: result.success,
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to clear user files (test)', error);
            throw new common_1.HttpException('Failed to clear user files', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.AppController = AppController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", String)
], AppController.prototype, "getHello", null);
__decorate([
    (0, common_1.Get)('files'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "listUserFiles", null);
__decorate([
    (0, common_1.Get)('files-test'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppController.prototype, "listUserFilesTest", null);
__decorate([
    (0, common_1.Delete)('files'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "clearUserFiles", null);
__decorate([
    (0, common_1.Delete)('files-test'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppController.prototype, "clearUserFilesTest", null);
exports.AppController = AppController = AppController_1 = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [app_service_1.AppService,
        file_service_1.FileService])
], AppController);
//# sourceMappingURL=app.controller.js.map
{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\sui.move", "definition_location": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 218, "end": 221}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "sui"], "struct_map": {"0": {"definition_location": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 862, "end": 865}, "type_parameters": [], "fields": [{"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 862, "end": 865}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1024, "end": 1625}, "definition_location": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1028, "end": 1031}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1032, "end": 1035}]], "returns": [{"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1054, "end": 1066}], "locals": [["metadata#1#0", {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1194, "end": 1202}], ["supply#1#0", {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1472, "end": 1478}], ["total_sui#1#0", {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1523, "end": 1532}], ["treasury#1#0", {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1184, "end": 1192}]], "nops": {}, "code_map": {"0": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1082, "end": 1085}, "2": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1082, "end": 1094}, "3": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1098, "end": 1102}, "4": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1095, "end": 1097}, "5": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1074, "end": 1122}, "9": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1104, "end": 1121}, "10": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1074, "end": 1122}, "11": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1137, "end": 1140}, "13": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1137, "end": 1148}, "14": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1152, "end": 1153}, "15": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1149, "end": 1151}, "16": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1129, "end": 1170}, "20": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1155, "end": 1169}, "21": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1129, "end": 1170}, "22": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1238, "end": 1244}, "24": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1255, "end": 1256}, "25": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1267, "end": 1273}, "26": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1284, "end": 1290}, "27": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1360, "end": 1363}, "28": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1374, "end": 1388}, "29": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1399, "end": 1402}, "30": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1206, "end": 1410}, "31": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1194, "end": 1202}, "32": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1184, "end": 1192}, "33": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1448, "end": 1456}, "34": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1417, "end": 1457}, "35": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1481, "end": 1489}, "36": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1481, "end": 1512}, "37": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1468, "end": 1478}, "38": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1535, "end": 1541}, "39": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1558, "end": 1575}, "40": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1535, "end": 1576}, "41": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1523, "end": 1532}, "42": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1583, "end": 1589}, "43": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1583, "end": 1606}, "45": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1613, "end": 1622}}, "is_native": false}, "1": {"location": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1629, "end": 1744}, "definition_location": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1646, "end": 1654}, "type_parameters": [], "parameters": [["c#0#0", {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1655, "end": 1656}], ["recipient#0#0", {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1675, "end": 1684}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1728, "end": 1729}, "1": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1731, "end": 1740}, "2": {"file_hash": [169, 29, 143, 231, 192, 221, 22, 200, 194, 102, 6, 206, 58, 153, 166, 123, 205, 163, 140, 12, 65, 56, 127, 177, 90, 222, 22, 47, 185, 218, 25, 140], "start": 1702, "end": 1741}}, "is_native": false}}, "constant_map": {"EAlreadyMinted": 0, "ENotSystemAddress": 1, "MIST_PER_SUI": 2, "TOTAL_SUPPLY_MIST": 4, "TOTAL_SUPPLY_SUI": 3}}
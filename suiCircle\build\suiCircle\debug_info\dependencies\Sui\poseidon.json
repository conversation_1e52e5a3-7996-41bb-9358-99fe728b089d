{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\crypto\\poseidon.move", "definition_location": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 184, "end": 192}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "poseidon"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 999, "end": 1445}, "definition_location": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1010, "end": 1024}, "type_parameters": [], "parameters": [["data#0#0", {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1025, "end": 1029}]], "returns": [{"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1047, "end": 1051}], "locals": [["%#1", {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1407, "end": 1430}], ["b#1#0", {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1075, "end": 1076}], ["i#1#0", {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1068, "end": 1069}], ["l#1#0", {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1078, "end": 1079}]], "nops": {}, "code_map": {"0": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1084, "end": 1085}, "1": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1087, "end": 1095}, "2": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1097, "end": 1101}, "3": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1097, "end": 1110}, "4": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1078, "end": 1079}, "5": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1071, "end": 1076}, "6": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1064, "end": 1069}, "7": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1126, "end": 1127}, "8": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1130, "end": 1131}, "9": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1128, "end": 1129}, "10": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1118, "end": 1145}, "14": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1133, "end": 1144}, "15": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1118, "end": 1145}, "16": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1159, "end": 1160}, "17": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1163, "end": 1164}, "18": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1161, "end": 1162}, "19": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1152, "end": 1346}, "20": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1198, "end": 1202}, "21": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1203, "end": 1204}, "22": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1197, "end": 1205}, "23": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1224, "end": 1238}, "24": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1241, "end": 1250}, "25": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1239, "end": 1240}, "26": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1216, "end": 1271}, "30": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1252, "end": 1270}, "31": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1216, "end": 1271}, "32": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1282, "end": 1283}, "33": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1309, "end": 1313}, "34": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1314, "end": 1315}, "35": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1308, "end": 1316}, "36": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1294, "end": 1317}, "37": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1282, "end": 1318}, "38": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1333, "end": 1334}, "39": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1337, "end": 1338}, "40": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1335, "end": 1336}, "41": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1329, "end": 1330}, "42": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1152, "end": 1346}, "43": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1353, "end": 1400}, "45": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1397, "end": 1399}, "46": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1373, "end": 1400}, "47": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1407, "end": 1430}, "50": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1407, "end": 1442}}, "is_native": false}, "1": {"location": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1645, "end": 1719}, "definition_location": {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1656, "end": 1679}, "type_parameters": [], "parameters": [["data#0#0", {"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1680, "end": 1684}]], "returns": [{"file_hash": [164, 0, 236, 101, 170, 223, 136, 57, 52, 172, 107, 28, 252, 72, 118, 67, 217, 206, 7, 123, 194, 17, 193, 4, 127, 170, 25, 165, 241, 179, 221, 20], "start": 1708, "end": 1718}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"BN254_MAX": 2, "EEmptyInput": 1, "ENonCanonicalInput": 0}}
---
compiled_package_info:
  package_name: suiCircle
  address_alias_instantiation:
    bridge: 000000000000000000000000000000000000000000000000000000000000000b
    std: "0000000000000000000000000000000000000000000000000000000000000001"
    sui: "0000000000000000000000000000000000000000000000000000000000000002"
    sui_system: "0000000000000000000000000000000000000000000000000000000000000003"
    suicircle: "0000000000000000000000000000000000000000000000000000000000000000"
  source_digest: 8D42BB57980BC89C7227660AA321771D714A3B9195FAF45E3C2213F7C72A5256
  build_flags:
    dev_mode: false
    test_mode: false
    generate_docs: false
    save_disassembly: false
    install_dir: ~
    force_recompilation: false
    lock_file: ".\\Move.lock"
    fetch_deps_only: false
    skip_fetch_latest_git_deps: false
    default_flavor: sui
    default_edition: ~
    deps_as_root: false
    silence_warnings: false
    warnings_are_errors: false
    json_errors: false
    additional_named_addresses: {}
    lint_flag:
      no_lint: false
      lint: false
    modes: []
    implicit_dependencies:
      Bridge:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: 2c930c25f8d3
              subdir: crates/sui-framework/packages/bridge
          subst: ~
          digest: ~
          dep_override: true
      MoveStdlib:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: 2c930c25f8d3
              subdir: crates/sui-framework/packages/move-stdlib
          subst: ~
          digest: ~
          dep_override: true
      Sui:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: 2c930c25f8d3
              subdir: crates/sui-framework/packages/sui-framework
          subst: ~
          digest: ~
          dep_override: true
      SuiSystem:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: 2c930c25f8d3
              subdir: crates/sui-framework/packages/sui-system
          subst: ~
          digest: ~
          dep_override: true
    force_lock_file: false
dependencies:
  - Bridge
  - MoveStdlib
  - Sui
  - SuiSystem

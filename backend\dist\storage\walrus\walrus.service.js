"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WalrusService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalrusService = void 0;
const common_1 = require("@nestjs/common");
const walrus_1 = require("@mysten/walrus");
const client_1 = require("@mysten/sui/client");
const ed25519_1 = require("@mysten/sui/keypairs/ed25519");
let WalrusService = WalrusService_1 = class WalrusService {
    logger = new common_1.Logger(WalrusService_1.name);
    walrusClient;
    suiClient;
    signer = null;
    useUploadRelay;
    constructor() {
        this.initializeClients();
    }
    initializeClients() {
        try {
            const network = process.env.WALRUS_NETWORK || 'testnet';
            const suiRpcUrl = process.env.SUI_RPC_URL || (0, client_1.getFullnodeUrl)('testnet');
            this.useUploadRelay = process.env.WALRUS_USE_UPLOAD_RELAY === 'true';
            this.suiClient = new client_1.SuiClient({
                url: suiRpcUrl,
            });
            if (process.env.WALRUS_PRIVATE_KEY) {
                try {
                    this.signer = ed25519_1.Ed25519Keypair.fromSecretKey(process.env.WALRUS_PRIVATE_KEY);
                    this.logger.log('Walrus signer initialized successfully');
                }
                catch (error) {
                    this.logger.error('Failed to initialize Walrus signer:', error);
                    throw new Error('Invalid WALRUS_PRIVATE_KEY format');
                }
            }
            else if (this.useUploadRelay) {
                this.signer = new ed25519_1.Ed25519Keypair();
                this.logger.log('Dummy signer created for upload relay mode');
            }
            const walrusConfig = {
                network: network,
                suiClient: this.suiClient,
                storageNodeClientOptions: {
                    timeout: 60_000,
                    onError: (error) => {
                        this.logger.warn('Walrus storage node error:', error.message);
                    },
                },
            };
            if (this.useUploadRelay) {
                const relayUrl = process.env.WALRUS_UPLOAD_RELAY_URL;
                const maxTip = parseInt(process.env.WALRUS_MAX_TIP || '1000');
                if (!relayUrl) {
                    throw new Error('WALRUS_UPLOAD_RELAY_URL is required when using upload relay');
                }
                walrusConfig.uploadRelay = {
                    host: relayUrl,
                    sendTip: {
                        max: maxTip,
                    },
                };
                this.logger.log(`Walrus upload relay configured: ${relayUrl}`);
            }
            this.walrusClient = new walrus_1.WalrusClient(walrusConfig);
            this.logger.log(`Walrus client initialized successfully (${this.useUploadRelay ? 'upload relay' : 'direct upload'} mode)`);
        }
        catch (error) {
            this.logger.error('Failed to initialize Walrus clients:', error);
            throw error;
        }
    }
    async uploadFile(fileData, filename, contentType) {
        try {
            this.logger.log(`Starting upload for file: ${filename}, size: ${fileData.length} bytes`);
            const data = fileData instanceof Buffer ? new Uint8Array(fileData) : fileData;
            if (process.env.NODE_ENV === 'development' && !process.env.WALRUS_PRIVATE_KEY && !this.useUploadRelay) {
                return this.uploadFileMock(data, filename);
            }
            let result;
            if (this.useUploadRelay) {
                result = await this.uploadViaRelay(data, filename, contentType);
            }
            else {
                result = await this.uploadDirect(data, filename, contentType);
            }
            if (result.success) {
                this.logger.log(`✅ File uploaded successfully: ${filename} -> ${result.blobId}`);
                this.logger.log(`📊 File size: ${data.length} bytes`);
                this.logger.log(`🔗 Walrus CID: ${result.blobId}`);
            }
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to upload file ${filename}:`, error);
            return {
                success: false,
                error: error.message,
            };
        }
    }
    async uploadViaRelay(data, filename, contentType) {
        try {
            const walrusFile = walrus_1.WalrusFile.from({
                contents: data,
                identifier: filename,
                tags: {
                    'content-type': contentType || 'application/octet-stream',
                    'upload-timestamp': new Date().toISOString(),
                },
            });
            if (!this.signer) {
                throw new Error('No signer available for upload relay');
            }
            const results = await this.walrusClient.writeFiles({
                files: [walrusFile],
                epochs: parseInt(process.env.WALRUS_STORAGE_EPOCHS || '5'),
                deletable: true,
                signer: this.signer,
            });
            if (results && results.length > 0) {
                return {
                    success: true,
                    blobId: results[0].blobId,
                    size: data.length,
                };
            }
            else {
                throw new Error('No results returned from upload relay');
            }
        }
        catch (error) {
            this.logger.error('Upload relay failed:', error);
            return {
                success: false,
                error: `Upload relay failed: ${error.message}`,
            };
        }
    }
    async uploadDirect(data, filename, contentType) {
        try {
            if (!this.signer) {
                throw new Error('No signer configured for direct upload. Set WALRUS_PRIVATE_KEY or enable upload relay.');
            }
            const result = await this.walrusClient.writeBlob({
                blob: data,
                deletable: true,
                epochs: parseInt(process.env.WALRUS_STORAGE_EPOCHS || '5'),
                signer: this.signer,
            });
            return {
                success: true,
                blobId: result.blobId,
                size: data.length,
            };
        }
        catch (error) {
            this.logger.error('Direct upload failed:', error);
            return {
                success: false,
                error: `Direct upload failed: ${error.message}`,
            };
        }
    }
    async uploadFileMock(data, filename) {
        this.logger.warn('Using mock upload - configure WALRUS_PRIVATE_KEY or upload relay for production');
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
        const simulatedBlobId = this.generateMockBlobId(filename, data.length);
        this.logger.log(`✅ File upload simulated: ${filename} -> ${simulatedBlobId}`);
        return {
            success: true,
            blobId: simulatedBlobId,
            size: data.length,
        };
    }
    generateMockBlobId(filename, size) {
        const timestamp = Date.now();
        const hash = Buffer.from(`${filename}-${size}-${timestamp}`).toString('base64url');
        return `mock_${hash.substring(0, 32)}`;
    }
    validateConfiguration() {
        const errors = [];
        const isProduction = process.env.NODE_ENV === 'production';
        if (isProduction) {
            if (!this.useUploadRelay && !this.signer) {
                errors.push('Production mode requires either WALRUS_PRIVATE_KEY or WALRUS_USE_UPLOAD_RELAY=true');
            }
            if (this.useUploadRelay && !process.env.WALRUS_UPLOAD_RELAY_URL) {
                errors.push('Upload relay mode requires WALRUS_UPLOAD_RELAY_URL');
            }
        }
        if (!process.env.SUI_RPC_URL) {
            errors.push('SUI_RPC_URL is required');
        }
        return {
            valid: errors.length === 0,
            errors,
        };
    }
    getConfigurationStatus() {
        return {
            mode: this.useUploadRelay ? 'upload-relay' : (this.signer ? 'direct-upload' : 'mock'),
            network: process.env.WALRUS_NETWORK || 'testnet',
            hasPrivateKey: !!this.signer,
            uploadRelayUrl: this.useUploadRelay ? process.env.WALRUS_UPLOAD_RELAY_URL : undefined,
        };
    }
    async getWalletInfo() {
        try {
            if (!this.signer) {
                return { error: 'No signer available' };
            }
            const address = this.signer.toSuiAddress();
            const suiBalance = await this.suiClient.getBalance({
                owner: address,
                coinType: '0x2::sui::SUI',
            });
            const walTokenType = '0x8270feb7375eee355e64fdb69c50abb6b5f9393a722883c1cf45f8e26048810a::wal::WAL';
            let walBalance;
            try {
                walBalance = await this.suiClient.getBalance({
                    owner: address,
                    coinType: walTokenType,
                });
            }
            catch (error) {
                walBalance = { totalBalance: '0', coinType: walTokenType, coinObjectCount: 0 };
            }
            return {
                address,
                suiBalance: suiBalance.totalBalance,
                walBalance: walBalance.totalBalance,
            };
        }
        catch (error) {
            this.logger.error('Failed to get wallet info:', error);
            return { error: error.message };
        }
    }
    async downloadFile(blobId) {
        try {
            this.logger.log(`Downloading file with blobId: ${blobId}`);
            if (blobId.startsWith('mock_')) {
                return this.downloadFileMock(blobId);
            }
            const data = await this.walrusClient.readBlob({ blobId });
            this.logger.log(`✅ File downloaded successfully, size: ${data.length} bytes`);
            return {
                success: true,
                data,
            };
        }
        catch (error) {
            this.logger.error(`Failed to download file with blobId ${blobId}:`, error);
            if (error.message.includes('network') || error.message.includes('timeout')) {
                return {
                    success: false,
                    error: `Network error downloading from Walrus: ${error.message}. Please check your connection and try again.`,
                };
            }
            if (error.message.includes('not found') || error.message.includes('404')) {
                return {
                    success: false,
                    error: `File not found in Walrus storage. The blob ID may be invalid or the file may have expired.`,
                };
            }
            return {
                success: false,
                error: error.message,
            };
        }
    }
    async downloadFileMock(blobId) {
        this.logger.warn('Downloading mock file - this is development mode');
        await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
        const mockContent = `Mock file content for ${blobId}
Generated at: ${new Date().toISOString()}
This is simulated data from Walrus storage.
Original blob ID: ${blobId}
File content would be retrieved from Walrus network.`;
        const data = new Uint8Array(Buffer.from(mockContent, 'utf-8'));
        this.logger.log(`✅ Mock file downloaded successfully, size: ${data.length} bytes`);
        return {
            success: true,
            data,
        };
    }
    async blobExists(blobId) {
        try {
            await this.walrusClient.readBlob({ blobId });
            return true;
        }
        catch (error) {
            this.logger.debug(`Blob ${blobId} does not exist or is not accessible`);
            return false;
        }
    }
    async getBlobInfo(blobId) {
        try {
            const data = await this.walrusClient.readBlob({ blobId });
            return {
                exists: true,
                size: data.length,
            };
        }
        catch (error) {
            return {
                exists: false,
                error: error.message,
            };
        }
    }
};
exports.WalrusService = WalrusService;
exports.WalrusService = WalrusService = WalrusService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], WalrusService);
//# sourceMappingURL=walrus.service.js.map
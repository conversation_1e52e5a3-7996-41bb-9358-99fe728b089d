{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\crypto\\ecvrf.move", "definition_location": {"file_hash": [119, 175, 35, 139, 145, 183, 138, 132, 236, 136, 49, 184, 115, 105, 76, 245, 46, 157, 253, 248, 225, 240, 49, 16, 110, 151, 50, 189, 151, 209, 204, 190], "start": 90, "end": 95}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "ecvrf"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [119, 175, 35, 139, 145, 183, 138, 132, 236, 136, 49, 184, 115, 105, 76, 245, 46, 157, 253, 248, 225, 240, 49, 16, 110, 151, 50, 189, 151, 209, 204, 190], "start": 799, "end": 951}, "definition_location": {"file_hash": [119, 175, 35, 139, 145, 183, 138, 132, 236, 136, 49, 184, 115, 105, 76, 245, 46, 157, 253, 248, 225, 240, 49, 16, 110, 151, 50, 189, 151, 209, 204, 190], "start": 817, "end": 829}, "type_parameters": [], "parameters": [["hash#0#0", {"file_hash": [119, 175, 35, 139, 145, 183, 138, 132, 236, 136, 49, 184, 115, 105, 76, 245, 46, 157, 253, 248, 225, 240, 49, 16, 110, 151, 50, 189, 151, 209, 204, 190], "start": 836, "end": 840}], ["alpha_string#0#0", {"file_hash": [119, 175, 35, 139, 145, 183, 138, 132, 236, 136, 49, 184, 115, 105, 76, 245, 46, 157, 253, 248, 225, 240, 49, 16, 110, 151, 50, 189, 151, 209, 204, 190], "start": 860, "end": 872}], ["public_key#0#0", {"file_hash": [119, 175, 35, 139, 145, 183, 138, 132, 236, 136, 49, 184, 115, 105, 76, 245, 46, 157, 253, 248, 225, 240, 49, 16, 110, 151, 50, 189, 151, 209, 204, 190], "start": 892, "end": 902}], ["proof#0#0", {"file_hash": [119, 175, 35, 139, 145, 183, 138, 132, 236, 136, 49, 184, 115, 105, 76, 245, 46, 157, 253, 248, 225, 240, 49, 16, 110, 151, 50, 189, 151, 209, 204, 190], "start": 922, "end": 927}]], "returns": [{"file_hash": [119, 175, 35, 139, 145, 183, 138, 132, 236, 136, 49, 184, 115, 105, 76, 245, 46, 157, 253, 248, 225, 240, 49, 16, 110, 151, 50, 189, 151, 209, 204, 190], "start": 946, "end": 950}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EInvalidHashLength": 0, "EInvalidProofEncoding": 2, "EInvalidPublicKeyEncoding": 1}}
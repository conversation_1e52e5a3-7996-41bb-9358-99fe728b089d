{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\crypto\\ecdsa_k1.move", "definition_location": {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 90, "end": 98}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "ecdsa_k1"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 1739, "end": 1860}, "definition_location": {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 1757, "end": 1776}, "type_parameters": [], "parameters": [["signature#0#0", {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 1783, "end": 1792}], ["msg#0#0", {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 1812, "end": 1815}], ["hash#0#0", {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 1835, "end": 1839}]], "returns": [{"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 1849, "end": 1859}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "1": {"location": {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 2125, "end": 2194}, "definition_location": {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 2143, "end": 2160}, "type_parameters": [], "parameters": [["pubkey#0#0", {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 2161, "end": 2167}]], "returns": [{"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 2183, "end": 2193}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "2": {"location": {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 2895, "end": 3037}, "definition_location": {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 2913, "end": 2929}, "type_parameters": [], "parameters": [["signature#0#0", {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 2936, "end": 2945}], ["public_key#0#0", {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 2965, "end": 2975}], ["msg#0#0", {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 2995, "end": 2998}], ["hash#0#0", {"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 3018, "end": 3022}]], "returns": [{"file_hash": [236, 77, 35, 188, 236, 247, 131, 202, 129, 101, 74, 16, 172, 204, 66, 100, 79, 32, 155, 53, 170, 9, 138, 2, 32, 164, 127, 95, 183, 255, 254, 197], "start": 3032, "end": 3036}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EFailToRecoverPubKey": 0, "EInvalidPubKey": 2, "EInvalidSignature": 1, "KECCAK256": 3, "SHA256": 4}}
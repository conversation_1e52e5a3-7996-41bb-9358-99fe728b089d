{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-system\\sources\\validator_wrapper.move", "definition_location": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 97, "end": 114}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000003", "validator_wrapper"], "struct_map": {"0": {"definition_location": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 249, "end": 265}, "type_parameters": [], "fields": [{"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 283, "end": 288}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 347, "end": 527}, "definition_location": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 367, "end": 376}, "type_parameters": [], "parameters": [["validator#0#0", {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 377, "end": 386}], ["ctx#0#0", {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 399, "end": 402}]], "returns": [{"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 421, "end": 437}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 498, "end": 499}, "1": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 501, "end": 510}, "2": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 512, "end": 515}, "3": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 480, "end": 516}, "4": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 445, "end": 524}}, "is_native": false}, "1": {"location": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 666, "end": 828}, "definition_location": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 686, "end": 714}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 715, "end": 719}]], "returns": [{"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 745, "end": 759}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 767, "end": 771}, "2": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 767, "end": 791}, "3": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 798, "end": 802}, "4": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 798, "end": 808}, "5": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 798, "end": 825}}, "is_native": false}, "2": {"location": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 898, "end": 1061}, "definition_location": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 918, "end": 925}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 926, "end": 930}]], "returns": [{"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 951, "end": 960}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 986, "end": 991}, "1": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 968, "end": 992}, "2": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1032, "end": 1036}, "3": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1003, "end": 1029}, "4": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1043, "end": 1058}}, "is_native": false}, "3": {"location": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1284, "end": 1494}, "definition_location": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1288, "end": 1305}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1306, "end": 1310}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1352, "end": 1356}, "1": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1352, "end": 1366}, "2": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1471, "end": 1472}, "3": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1468, "end": 1470}, "4": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1452, "end": 1490}, "6": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1474, "end": 1489}, "7": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1452, "end": 1490}, "8": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1490, "end": 1491}}, "is_native": false}, "4": {"location": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1498, "end": 1570}, "definition_location": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1502, "end": 1509}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1510, "end": 1514}]], "returns": [{"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1536, "end": 1539}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1547, "end": 1551}, "1": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1547, "end": 1557}, "2": {"file_hash": [17, 26, 139, 76, 29, 35, 235, 128, 83, 148, 64, 58, 54, 34, 203, 3, 84, 173, 66, 34, 94, 36, 35, 154, 99, 30, 243, 80, 197, 237, 250, 95], "start": 1547, "end": 1567}}, "is_native": false}}, "constant_map": {"EInvalidVersion": 0}}
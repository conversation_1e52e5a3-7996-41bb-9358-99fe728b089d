{"version": 3, "file": "zklogin.config.js", "sourceRoot": "", "sources": ["../../src/config/zklogin.config.ts"], "names": [], "mappings": ";;;AAmKA,sDAuCC;AA3IY,QAAA,oBAAoB,GAAkB;IACjD,KAAK,EAAE;QACL,MAAM,EAAE;YACN,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;YAC5C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,4CAA4C;SAC7F;QACD,QAAQ,EAAE;YACR,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,EAAE;YAC9C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,8CAA8C;SACjG;QACD,MAAM,EAAE;YACN,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;YAC5C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,4CAA4C;SAC7F;QACD,KAAK,EAAE;YACL,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE;YAC3C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,2CAA2C;SAC3F;QACD,MAAM,EAAE;YACN,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;YAC5C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,wBAAwB;SACzE;KACF;IAED,GAAG,EAAE;QACH,OAAO,EAAG,OAAO,CAAC,GAAG,CAAC,WAAgD,IAAI,SAAS;QACnF,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,qCAAqC;QACxE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB;QAC3C,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB;KAC9C;IAED,OAAO,EAAE;QACP,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,mCAAmC;QACrE,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,IAAI,CAAC;QACzD,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,sCAAsC;KACpF;IAED,GAAG,EAAE;QACH,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,sCAAsC;QACxE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK;KAC/C;CACF,CAAC;AAGF,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAC5C,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAC/D,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AACrE,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,4BAAoB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACjE,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;AAK3C,IAAY,aAMX;AAND,WAAY,aAAa;IACvB,kCAAiB,CAAA;IACjB,sCAAqB,CAAA;IACrB,kCAAiB,CAAA;IACjB,gCAAe,CAAA;IACf,kCAAiB,CAAA;AACnB,CAAC,EANW,aAAa,6BAAb,aAAa,QAMxB;AAKY,QAAA,oBAAoB,GAAG;IAClC,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;QACtB,OAAO,EAAE,8CAA8C;QACvD,QAAQ,EAAE,qCAAqC;QAC/C,WAAW,EAAE,+CAA+C;QAC5D,KAAK,EAAE,sBAAsB;KAC9B;IACD,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;QACxB,OAAO,EAAE,6CAA6C;QACtD,QAAQ,EAAE,qDAAqD;QAC/D,WAAW,EAAE,+BAA+B;QAC5C,KAAK,EAAE,OAAO;KACf;IACD,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;QACtB,OAAO,EAAE,uCAAuC;QAChD,QAAQ,EAAE,mCAAmC;QAC7C,WAAW,EAAE,mCAAmC;QAChD,KAAK,EAAE,wBAAwB;KAChC;IACD,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;QACrB,OAAO,EAAE,0CAA0C;QACnD,QAAQ,EAAE,sCAAsC;QAChD,WAAW,EAAE,EAAE;QACf,KAAK,EAAE,YAAY;KACpB;IACD,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;QACtB,OAAO,EAAE,0CAA0C;QACnD,QAAQ,EAAE,6CAA6C;QACvD,WAAW,EAAE,6BAA6B;QAC1C,KAAK,EAAE,YAAY;KACpB;CACF,CAAC;AAKF,SAAgB,qBAAqB,CAAC,MAAqB;IACzD,MAAM,MAAM,GAAa,EAAE,CAAC;IAG5B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,EAAE;QAC5D,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACvB,MAAM,CAAC,IAAI,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;QACtD,CAAC;IACH,CAAC,CAAC,CAAC;IAGH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;QACvB,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACrC,CAAC;IAGD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,mCAAmC,EAAE,CAAC;QAChE,OAAO,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;IAC3E,CAAC;IAGD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;QACvB,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACpC,CAAC;IAED,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,KAAK,sCAAsC,EAAE,CAAC;QACjE,OAAO,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;IACjF,CAAC;IAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CAAC,iCAAiC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACxE,CAAC;AACH,CAAC"}
{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-system\\sources\\stake_subsidy.move", "definition_location": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 97, "end": 110}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000003", "stake_subsidy"], "struct_map": {"0": {"definition_location": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 304, "end": 316}, "type_parameters": [], "fields": [{"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 423, "end": 430}, {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 528, "end": 548}, {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 685, "end": 712}, {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 805, "end": 832}, {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 965, "end": 992}, {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1061, "end": 1073}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1086, "end": 1741}, "definition_location": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1106, "end": 1112}, "type_parameters": [], "parameters": [["balance#0#0", {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1119, "end": 1126}], ["initial_distribution_amount#0#0", {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1147, "end": 1174}], ["stake_subsidy_period_length#0#0", {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1186, "end": 1213}], ["stake_subsidy_decrease_rate#0#0", {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1225, "end": 1252}], ["ctx#0#0", {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1264, "end": 1267}]], "returns": [{"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1289, "end": 1301}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1367, "end": 1394}, "1": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1398, "end": 1421}, "2": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1398, "end": 1428}, "3": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1395, "end": 1397}, "4": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1349, "end": 1475}, "8": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1439, "end": 1467}, "9": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1349, "end": 1475}, "10": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1508, "end": 1515}, "11": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1548, "end": 1549}, "12": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1589, "end": 1616}, "13": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1627, "end": 1654}, "14": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1665, "end": 1692}, "15": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1726, "end": 1729}, "16": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1717, "end": 1730}, "17": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1484, "end": 1738}}, "is_native": false}, "1": {"location": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1817, "end": 2779}, "definition_location": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1837, "end": 1850}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1851, "end": 1855}]], "returns": [{"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 1877, "end": 1889}], "locals": [["decrease_amount#1#0", {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2472, "end": 2487}], ["stake_subsidy#1#0", {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2195, "end": 2208}], ["to_withdraw#1#0", {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2063, "end": 2074}]], "nops": {}, "code_map": {"0": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2077, "end": 2081}, "1": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2077, "end": 2109}, "3": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2114, "end": 2118}, "4": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2114, "end": 2126}, "5": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2114, "end": 2134}, "6": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2077, "end": 2135}, "7": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2063, "end": 2074}, "8": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2211, "end": 2215}, "9": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2211, "end": 2223}, "10": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2230, "end": 2241}, "11": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2211, "end": 2242}, "12": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2195, "end": 2208}, "13": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2277, "end": 2281}, "14": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2277, "end": 2302}, "16": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2305, "end": 2306}, "17": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2303, "end": 2304}, "18": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2249, "end": 2253}, "19": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2249, "end": 2274}, "20": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2249, "end": 2306}, "21": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2390, "end": 2394}, "22": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2390, "end": 2415}, "24": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2418, "end": 2422}, "25": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2418, "end": 2450}, "27": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2416, "end": 2417}, "28": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2454, "end": 2455}, "29": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2451, "end": 2453}, "30": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2386, "end": 2754}, "31": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2503, "end": 2507}, "32": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2503, "end": 2535}, "34": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2503, "end": 2543}, "35": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2560, "end": 2564}, "36": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2560, "end": 2592}, "38": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2560, "end": 2600}, "39": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2557, "end": 2558}, "40": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2604, "end": 2627}, "41": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2602, "end": 2603}, "42": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2472, "end": 2487}, "43": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2688, "end": 2692}, "44": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2688, "end": 2720}, "46": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2724, "end": 2739}, "47": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2724, "end": 2746}, "48": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2721, "end": 2722}, "49": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2640, "end": 2644}, "50": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2640, "end": 2672}, "51": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2640, "end": 2747}, "52": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2386, "end": 2754}, "55": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2763, "end": 2776}}, "is_native": false}, "2": {"location": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2869, "end": 3003}, "definition_location": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2880, "end": 2908}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2909, "end": 2913}]], "returns": [{"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2931, "end": 2934}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2942, "end": 2946}, "1": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2942, "end": 2974}, "3": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2979, "end": 2983}, "4": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2979, "end": 2991}, "5": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2979, "end": 2999}, "6": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 2942, "end": 3000}}, "is_native": false}, "3": {"location": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 3068, "end": 3174}, "definition_location": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 3088, "end": 3112}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 3113, "end": 3117}]], "returns": [{"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 3135, "end": 3138}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 3146, "end": 3150}, "1": {"file_hash": [235, 79, 69, 158, 81, 128, 129, 147, 203, 211, 111, 241, 187, 30, 82, 111, 135, 124, 93, 98, 60, 113, 123, 55, 221, 143, 154, 142, 0, 33, 31, 145], "start": 3146, "end": 3171}}, "is_native": false}}, "constant_map": {"BASIS_POINT_DENOMINATOR": 1, "ESubsidyDecreaseRateTooLarge": 0}}
{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-system\\sources\\storage_fund.move", "definition_location": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 97, "end": 109}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000003", "storage_fund"], "struct_map": {"0": {"definition_location": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 871, "end": 882}, "type_parameters": [], "fields": [{"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 900, "end": 928}, {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 949, "end": 971}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1038, "end": 1302}, "definition_location": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1058, "end": 1061}, "type_parameters": [], "parameters": [["initial_fund#0#0", {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1062, "end": 1074}]], "returns": [{"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1091, "end": 1102}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1229, "end": 1244}, "1": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1279, "end": 1291}, "2": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1110, "end": 1299}}, "is_native": false}, "1": {"location": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1409, "end": 3047}, "definition_location": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1429, "end": 1442}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1449, "end": 1453}], ["storage_charges#0#0", {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1478, "end": 1493}], ["storage_fund_reinvestment#0#0", {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1514, "end": 1539}], ["leftover_staking_rewards#0#0", {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1560, "end": 1584}], ["storage_rebate_amount#0#0", {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1605, "end": 1626}], ["non_refundable_storage_fee_amount#0#0", {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1638, "end": 1671}]], "returns": [{"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1682, "end": 1694}], "locals": [["non_refundable_storage_fee#1#0", {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2372, "end": 2398}]], "nops": {}, "code_map": {"0": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1818, "end": 1822}, "1": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1818, "end": 1845}, "2": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1851, "end": 1876}, "3": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1818, "end": 1877}, "5": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1884, "end": 1888}, "6": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1884, "end": 1911}, "7": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1917, "end": 1941}, "8": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 1884, "end": 1942}, "10": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2193, "end": 2197}, "11": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2193, "end": 2226}, "12": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2232, "end": 2247}, "13": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2193, "end": 2248}, "15": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2401, "end": 2405}, "16": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2401, "end": 2444}, "17": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2461, "end": 2494}, "18": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2401, "end": 2495}, "19": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2372, "end": 2398}, "20": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2504, "end": 2508}, "21": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2504, "end": 2531}, "22": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2537, "end": 2563}, "23": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2504, "end": 2564}, "25": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2783, "end": 2787}, "26": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2783, "end": 2816}, "27": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2823, "end": 2844}, "28": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 2783, "end": 2845}, "29": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3030, "end": 3044}}, "is_native": false}, "2": {"location": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3051, "end": 3167}, "definition_location": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3062, "end": 3090}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3091, "end": 3095}]], "returns": [{"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3112, "end": 3115}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3123, "end": 3127}, "1": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3123, "end": 3156}, "2": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3123, "end": 3164}}, "is_native": false}, "3": {"location": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3171, "end": 3310}, "definition_location": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3182, "end": 3195}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3196, "end": 3200}]], "returns": [{"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3217, "end": 3220}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3228, "end": 3232}, "1": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3228, "end": 3261}, "2": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3228, "end": 3269}, "3": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3272, "end": 3276}, "4": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3272, "end": 3299}, "5": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3272, "end": 3307}, "6": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3270, "end": 3271}, "7": {"file_hash": [75, 2, 144, 139, 125, 223, 108, 228, 109, 109, 145, 5, 110, 24, 62, 228, 225, 21, 67, 152, 124, 189, 225, 72, 208, 124, 104, 108, 238, 133, 65, 168], "start": 3228, "end": 3307}}, "is_native": false}}, "constant_map": {}}
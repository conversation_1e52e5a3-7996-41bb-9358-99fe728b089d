import { Response } from 'express';
import { FileService, FileUploadRequest } from './file.service';
export declare class FileController {
    private readonly fileService;
    private readonly logger;
    constructor(fileService: FileService);
    uploadFile(authorization: string, file: Express.Multer.File, user: any): Promise<{
        success: boolean;
        data: {
            fileCid: string;
            transactionDigest: string;
            walrusCid: string;
        };
        message: string;
    }>;
    uploadFileMetadata(authorization: string, uploadRequest: FileUploadRequest, user: any): Promise<{
        success: boolean;
        data: {
            fileCid: string;
            transactionDigest: string;
            walrusCid: string;
        };
        message: string;
    }>;
    getFileInfo(cid: string, authorization: string, user: any): Promise<{
        success: boolean;
        data: {
            fileMetadata: {
                filename: string;
                fileSize: number;
                uploadTimestamp: number;
                uploader: string;
            } | undefined;
            walrusCid: string | undefined;
        };
        message: string;
    }>;
    downloadFile(cid: string, authorization: string, user: any, res: Response): Promise<void>;
    grantFileAccess(cid: string, body: {
        recipientAddress: string;
    }, authorization: string, user: any): Promise<{
        success: boolean;
        data: {
            transactionDigest: string | undefined;
        };
        message: string;
    }>;
    revokeFileAccess(cid: string, body: {
        addressToRemove: string;
    }, authorization: string, user: any): Promise<{
        success: boolean;
        data: {
            transactionDigest: string | undefined;
        };
        message: string;
    }>;
    listUserFiles(authorization: string, user: any): Promise<{
        success: boolean;
        data: {
            files: {
                cid: string;
                filename: string;
                fileSize: number;
                uploadTimestamp: number;
                uploader: string;
                isOwner: boolean;
            }[];
        };
        message: string;
    }>;
    uploadFileTest(file: Express.Multer.File): Promise<{
        success: boolean;
        data: {
            fileCid: string;
            transactionDigest: string;
            walrusCid: string;
        };
        message: string;
    }>;
    downloadFileTest(cid: string, res: Response): Promise<void>;
    getWalrusStatus(): Promise<{
        success: boolean;
        data: {
            validation: {
                valid: boolean;
                errors: string[];
            };
            environment: string;
            mode: "mock" | "upload-relay" | "direct-upload";
            network: string;
            hasPrivateKey: boolean;
            uploadRelayUrl?: string;
        };
        message: string;
    }>;
    getWalletInfo(): Promise<{
        success: boolean;
        data: {
            address?: string;
            suiBalance?: string;
            walBalance?: string;
            error?: string;
        };
        message: string;
    }>;
    uploadEncryptedFile(authorization: string, file: Express.Multer.File, user: any): Promise<{
        success: boolean;
        data: {
            fileCid: string;
            transactionDigest: string;
            walrusCid: string;
        };
        message: string;
    }>;
    downloadEncryptedFile(cid: string, body: {
        secretKey: string;
    }, authorization: string, user: any, res: Response): Promise<void>;
    uploadEncryptedFileTest(file: Express.Multer.File): Promise<{
        success: boolean;
        data: {
            fileCid: string;
            transactionDigest: string;
            walrusCid: string;
        };
        message: string;
    }>;
    getSealStatus(): Promise<{
        success: boolean;
        data: {
            isReady: boolean;
            version: string;
        };
        message: string;
    }>;
    listUserFilesTest(): Promise<{
        success: boolean;
        files: {
            cid: string;
            filename: string;
            fileSize: number;
            uploadTimestamp: number;
            uploader: string;
            isOwner: boolean;
        }[];
        data: {
            files: {
                cid: string;
                filename: string;
                fileSize: number;
                uploadTimestamp: number;
                uploader: string;
                isOwner: boolean;
            }[];
        };
        message: string;
    }>;
    downloadSharedFile(shareId: string, authorization: string, res: Response): Promise<void>;
}

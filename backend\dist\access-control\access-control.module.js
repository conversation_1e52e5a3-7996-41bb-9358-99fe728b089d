"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccessControlModule = void 0;
const common_1 = require("@nestjs/common");
const access_control_controller_1 = require("./access-control.controller");
const access_control_service_1 = require("./access-control.service");
const sui_module_1 = require("../sui/sui.module");
const auth_module_1 = require("../auth/auth.module");
let AccessControlModule = class AccessControlModule {
};
exports.AccessControlModule = AccessControlModule;
exports.AccessControlModule = AccessControlModule = __decorate([
    (0, common_1.Module)({
        imports: [sui_module_1.SuiModule, auth_module_1.AuthModule],
        controllers: [access_control_controller_1.AccessControlController],
        providers: [access_control_service_1.AccessControlService],
        exports: [access_control_service_1.AccessControlService],
    })
], AccessControlModule);
//# sourceMappingURL=access-control.module.js.map
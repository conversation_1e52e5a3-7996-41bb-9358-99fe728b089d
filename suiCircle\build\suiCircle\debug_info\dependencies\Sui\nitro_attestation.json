{"version": 2, "from_file_path": "C:\\Users\\<USER>\\.move\\https___github_com_MystenLabs_sui_git_main\\crates\\sui-framework\\packages\\sui-framework\\sources\\crypto\\nitro_attestation.move", "definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 90, "end": 107}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "nitro_attestation"], "struct_map": {"0": {"definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 655, "end": 663}, "type_parameters": [], "fields": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 680, "end": 685}, {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 696, "end": 701}]}, "1": {"definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 784, "end": 808}, "type_parameters": [], "fields": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 870, "end": 879}, {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 977, "end": 986}, {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 1069, "end": 1075}, {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 1253, "end": 1257}, {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 1375, "end": 1385}, {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 1471, "end": 1480}, {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 1622, "end": 1627}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 1882, "end": 2064}, "definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 1892, "end": 1914}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 1915, "end": 1926}], ["clock#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 1940, "end": 1945}]], "returns": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 1956, "end": 1980}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2020, "end": 2032}, "1": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2054, "end": 2059}, "2": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2034, "end": 2060}, "3": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 1988, "end": 2061}}, "is_native": false}, "1": {"location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2068, "end": 2174}, "definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2079, "end": 2088}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2089, "end": 2100}]], "returns": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2130, "end": 2141}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2150, "end": 2161}, "1": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2149, "end": 2171}}, "is_native": false}, "2": {"location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2178, "end": 2277}, "definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2189, "end": 2198}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2199, "end": 2210}]], "returns": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2240, "end": 2244}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2253, "end": 2264}, "1": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2252, "end": 2274}}, "is_native": false}, "3": {"location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2281, "end": 2381}, "definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2292, "end": 2298}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2299, "end": 2310}]], "returns": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2340, "end": 2351}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2360, "end": 2371}, "1": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2359, "end": 2378}}, "is_native": false}, "4": {"location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2527, "end": 2629}, "definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2538, "end": 2542}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2543, "end": 2554}]], "returns": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2584, "end": 2601}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2610, "end": 2621}, "1": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2609, "end": 2626}}, "is_native": false}, "5": {"location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2633, "end": 2749}, "definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2644, "end": 2654}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2655, "end": 2666}]], "returns": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2696, "end": 2715}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2724, "end": 2735}, "1": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2723, "end": 2746}}, "is_native": false}, "6": {"location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2753, "end": 2867}, "definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2764, "end": 2773}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2774, "end": 2785}]], "returns": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2815, "end": 2834}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2843, "end": 2854}, "1": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2842, "end": 2864}}, "is_native": false}, "7": {"location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2871, "end": 2977}, "definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2882, "end": 2887}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2888, "end": 2899}]], "returns": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2929, "end": 2948}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2957, "end": 2968}, "1": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2956, "end": 2974}}, "is_native": false}, "8": {"location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2981, "end": 3041}, "definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2992, "end": 2997}, "type_parameters": [], "parameters": [["entry#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 2998, "end": 3003}]], "returns": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3017, "end": 3019}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3027, "end": 3032}, "1": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3027, "end": 3038}}, "is_native": false}, "9": {"location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3045, "end": 3115}, "definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3056, "end": 3061}, "type_parameters": [], "parameters": [["entry#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3062, "end": 3067}]], "returns": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3081, "end": 3092}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3101, "end": 3106}, "1": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3100, "end": 3112}}, "is_native": false}, "10": {"location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3149, "end": 3282}, "definition_location": {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3160, "end": 3191}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3198, "end": 3209}], ["current_timestamp#0#0", {"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3229, "end": 3246}]], "returns": [{"file_hash": [245, 68, 13, 148, 126, 137, 134, 33, 172, 189, 153, 90, 82, 43, 167, 238, 169, 237, 101, 123, 170, 137, 62, 221, 70, 17, 246, 111, 109, 109, 104, 180], "start": 3257, "end": 3281}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EInvalidPCRsError": 3, "ENotSupportedError": 0, "EParseError": 1, "EVerifyError": 2}}
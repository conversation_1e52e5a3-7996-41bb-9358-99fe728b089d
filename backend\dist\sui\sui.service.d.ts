import { ZkLoginProof, EphemeralKeyPair } from '../auth/zklogin.service';
import { AccessControlRule } from '../access-control/access-control.service';
export interface AccessControlInfo {
    fileCid: string;
    owner: string;
    conditionType: string;
    allowedEmails: string[];
    allowedAddresses: string[];
    allowedSuiNS?: string[];
    accessStartTime?: number;
    accessEndTime?: number;
    requireAllConditions: boolean;
    currentAccessCount: number;
    totalUserRecords: number;
}
export interface FileMetadata {
    cid: string;
    filename: string;
    fileSize: number;
    uploadTimestamp: number;
    uploader: string;
    authorizedAddresses: string[];
}
export interface SmartContractConfig {
    packageId: string;
    registryObjectId: string;
}
export interface ZkLoginTransactionParams {
    zkLoginProof: ZkLoginProof;
    ephemeralKeyPair: EphemeralKeyPair;
    userSalt: string;
    jwt: string;
}
export declare class SuiService {
    private readonly logger;
    private readonly suiClient;
    private readonly config;
    private accessControlStorage;
    constructor();
    uploadFile(zkLoginAddress: string, cid: string, filename: string, fileSize: number): Promise<string>;
    uploadFileWithZkLogin(cid: string, filename: string, fileSize: number, zkLoginParams: ZkLoginTransactionParams): Promise<string>;
    grantFileAccess(zkLoginAddress: string, fileCid: string, authorizedAddress: string): Promise<string>;
    isAuthorizedForFile(fileCid: string, address: string): Promise<boolean>;
    getFileMetadata(fileCid: string): Promise<FileMetadata | null>;
    revokeFileAccess(zkLoginAddress: string, fileCid: string, addressToRemove: string): Promise<string>;
    private executeZkLoginTransaction;
    private deriveZkLoginAddress;
    private getAddressSeed;
    grantFileAccessWithZkLogin(fileCid: string, authorizedAddress: string, zkLoginParams: ZkLoginTransactionParams): Promise<string>;
    revokeFileAccessWithZkLogin(fileCid: string, addressToRemove: string, zkLoginParams: ZkLoginTransactionParams): Promise<string>;
    createFileAccessControl(zkLoginAddress: string, fileCid: string, accessRule: AccessControlRule): Promise<string>;
    updateFileAccessControl(zkLoginAddress: string, fileCid: string, accessRule: AccessControlRule): Promise<string>;
    validateFileAccess(fileCid: string, userAddress: string, userEmail?: string, userSuiNS?: string): Promise<boolean>;
    getFileAccessControlInfo(fileCid: string): Promise<AccessControlInfo | null>;
}
